// 测试 PromptCommandParser 和 CrossOriginDownloader 功能
import { PromptCommandParser, CrossOriginDownloader } from './src/lib/utils'

// 测试解析功能
const testCommand = `https://example.com/image1.jpg https://example.com/image2.jpg 一个美丽的风景画 --ar 16:9 --v 6.1 --chaos 25 --quality 0.8 --sref https://example.com/style.jpg --cref https://example.com/face.jpg --raw --fast`

console.log('测试命令：', testCommand)
console.log('')

const result = PromptCommandParser.parse(testCommand)

if (result.success) {
  console.log('✅ 解析成功！')
  console.log('')
  console.log('格式化结果：')
  console.log(PromptCommandParser.formatResult(result.data))
  console.log('')
  
  // 验证命令
  const validationErrors = PromptCommandParser.validate(result.data)
  if (validationErrors.length === 0) {
    console.log('✅ 命令验证通过')
  } else {
    console.log('❌ 验证错误：')
    validationErrors.forEach(error => console.log(`  - ${error}`))
  }
} else {
  console.log('❌ 解析失败：')
  result.errors.forEach(error => console.log(`  - ${error}`))
}

console.log('')
console.log('=====================================')
console.log('')

// 测试构建器功能
console.log('测试构建器：')
try {
  const command = PromptCommandParser.builder()
    .setContentUrls(['https://example.com/content.jpg'])
    .setPrompt('一个可爱的小猫')
    .setAspectRatio(1, 1)
    .setModelVersion('6.1')
    .setChaos(30)
    .setQuality(0.9)
    .addStyleUrl('https://example.com/style.jpg')
    .setVideoMode(true)
    .setMotion('high')
    .setBatchSize(2)
    .build()

  console.log('✅ 构建成功！')
  console.log('构建的命令：', command.toString())
  console.log('')
  console.log('格式化结果：')
  console.log(PromptCommandParser.formatResult(command))
} catch (error) {
  console.log('❌ 构建失败：', error.message)
}

console.log('')
console.log('=====================================')
console.log('')

// 测试视频命令
console.log('测试视频命令：')
const videoCommand = `https://example.com/first-frame.jpg 将这张图片转换为动态视频 --ar 16:9 --motion high --bs 2 --end https://example.com/end-frame.jpg --loop --fast`

const videoResult = PromptCommandParser.parse(videoCommand)
if (videoResult.success) {
  console.log('✅ 视频命令解析成功！')
  console.log('格式化结果：')
  console.log(PromptCommandParser.formatResult(videoResult.data))
} else {
  console.log('❌ 视频命令解析失败：')
  videoResult.errors.forEach(error => console.log(`  - ${error}`))
}

console.log('')
console.log('=====================================')
console.log('')

// 测试 CrossOriginDownloader 功能
console.log('测试 CrossOriginDownloader:')

// 测试文件名提取
const testUrls = [
  'https://example.com/image.jpg',
  'https://example.com/photos/vacation.png',
  'https://example.com/downloads/my-file.jpeg',
  'https://example.com/no-extension',
  'https://example.com/path/to/file.gif'
]

testUrls.forEach(url => {
  const fileName = CrossOriginDownloader.extractFileName(url)
  console.log(`URL: ${url}`)
  console.log(`提取的文件名：${fileName}`)
  console.log('')
})

console.log('✅ CrossOriginDownloader 测试完成！')
console.log('所有功能测试完成！')