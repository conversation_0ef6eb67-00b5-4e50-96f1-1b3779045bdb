import { Info } from 'lucide-react'
import { useCallback, useEffect, useRef, useState } from 'react'
import { cn } from '@/lib/utils'
import { useForegroundStore } from '@/stores/foreground'

type ForegroundProps = {
  url: string
  loading: boolean
  disable?: boolean
  style?: React.CSSProperties
}

const Foreground = ({ url, loading, disable = false }: ForegroundProps) => {
  const { position, scale, setPosition, setScale } = useForegroundStore()
  const [isDragging, setIsDragging] = useState(false)
  const lastMousePos = useRef({ x: 0, y: 0 })
  // 新增状态用于跟踪鼠标位置和提示的显示
  const [mousePosition, setMousePosition] = useState({ x: 0, y: 0 })
  const [showTooltip, setShowTooltip] = useState(false)

  // 处理鼠标按下事件
  const handleMouseDown = (e: React.MouseEvent) => {
    if (disable) return
    setIsDragging(true)
    lastMousePos.current = { x: e.clientX, y: e.clientY }
  }

  // 处理鼠标移动事件
  const handleMouseMove = (e: React.MouseEvent) => {
    const rect = e.currentTarget.parentElement?.getBoundingClientRect()

    if (rect) {
      const relativeX = e.clientX - rect.left
      const relativeY = e.clientY - rect.top
      setMousePosition({ x: relativeX, y: relativeY })
    }

    if (isDragging) {
      const deltaX = e.clientX - lastMousePos.current.x
      const deltaY = e.clientY - lastMousePos.current.y
      setPosition({ x: position.x + deltaX, y: position.y + deltaY })
      lastMousePos.current = { x: e.clientX, y: e.clientY }
    }
  }

  // 处理鼠标松开事件
  const handleGlobalMouseUp = useCallback(() => {
    setIsDragging(false)
  }, [])

  // 在组件挂载时添加全局事件监听
  useEffect(() => {
    document.body.addEventListener('mouseup', handleGlobalMouseUp)

    // 在组件卸载时移除事件监听
    return () => {
      document.body.removeEventListener('mouseup', handleGlobalMouseUp)
    }
  }, [handleGlobalMouseUp])

  // 处理滚轮事件
  const handleWheel = (e: React.WheelEvent) => {
    e.stopPropagation()
    const delta = e.deltaY < 0 ? 0.1 : -0.1
    setScale(Math.min(Math.max(scale + delta, 0.1), 2))
  }

  // 处理鼠标进入事件
  const handleMouseEnter = () => {
    setShowTooltip(true)
  }

  // 处理鼠标离开事件
  const handleMouseLeave = () => {
    setShowTooltip(false)
  }

  return (
    <>
      <img
        src={url}
        alt="blurred"
        className={cn(
          'max-w-full max-h-full object-contain touch-none select-none border-4 border-transparent',
          {
            'animate-pulse': loading,
            'cursor-grab': !isDragging && !disable,
            'cursor-grabbing': isDragging && !disable,
            'hover:border-blue-500': !disable,
          }
        )}
        style={{
          transform: `translate(${position.x}px, ${position.y}px) scale(${scale})`,
        }}
        id="foreground"
        onMouseDown={handleMouseDown}
        onMouseMove={handleMouseMove}
        onWheel={handleWheel}
        onMouseEnter={handleMouseEnter}
        onMouseLeave={handleMouseLeave}
        draggable="false"
        onDragStart={e => e.preventDefault()}
      />
      {!isDragging && showTooltip && !disable && (
        <div
          className="absolute pointer-events-none min-w-24 bg-black z-10 text-white px-2 py-1 rounded text-xs select-none"
          style={{
            left: mousePosition.x + 10,
            top: mousePosition.y + 10,
          }}
        >
          <Info className="size-3 inline-block mr-1/2 align-text-bottom" />{' '}
          按住鼠标拖动改变位置，滚轮改变大小
        </div>
      )}
    </>
  )
}

export default Foreground
