import { <PERSON><PERSON><PERSON>, Loader2 } from 'lucide-react'
import { useRef } from 'react'
import { toast } from 'sonner'
import type { ImageInfoType } from '@/config'
import { fileToBase64, uploadImage } from '@/lib/utils'
import type { UploadDataType } from '@/stores/advanced-edit'
import { useCommonStore } from '@/stores/common'
import { Button } from './ui/button'
import { Input } from './ui/input'
import { Label } from './ui/label'

export interface UploadProps {
  onSuccess?: (data: UploadDataType) => void
  onLoading?: (loading: boolean) => void
  className?: string
  text?: string
  size?: 'sm' | 'lg' | 'default' | 'icon' | null
}

const getImageInfo = (file: File): Promise<ImageInfoType> =>
  new Promise((resolve, reject) => {
    const image = new Image()
    image.onload = async () => {
      console.time('getImageInfo')
      let hasTransparentPart = false
      const canvas = document.createElement('canvas')
      const ctx = canvas.getContext('2d')

      if (!ctx) {
        hasTransparentPart = false
        return
      }

      canvas.width = image.width
      canvas.height = image.height
      ctx.drawImage(image, 0, 0)

      const imageData = ctx.getImageData(0, 0, canvas.width, canvas.height)
      const data = imageData.data

      for (let i = 0; i < data.length; i += 4) {
        if (data[i + 3] < 255) {
          hasTransparentPart = true
          break
        }
      }

      const base64 = await fileToBase64(file)
      resolve({
        width: image.width,
        height: image.height,
        base64: base64 as string,
        hasTransparentPart,
      })
      console.timeEnd('getImageInfo')
    }
    image.onerror = () => {
      reject(0)
    }
    image.src = URL.createObjectURL(file)
  })

export default function Upload({
  onSuccess,
  className,
  text = '添加图片',
  size = 'default',
}: UploadProps) {
  const fileInputRef = useRef<HTMLInputElement>(null)
  const { loading, setLoading } = useCommonStore()

  const handleFileChange = async (
    event: React.ChangeEvent<HTMLInputElement>
  ) => {
    const file = event.target.files?.[0]

    if (file) {
      // 最大文件支持 20M 大小
      if (file.size > 20 * 1024 * 1024) {
        toast.error('文件大小不能超过 20MB')
        if (fileInputRef.current) {
          fileInputRef.current.value = ''
        }
        return
      }
      setLoading(true)
      try {
        const fileInfo = await getImageInfo(file)

        if (Math.max(fileInfo.width, fileInfo.height) < 128) {
          toast.error('图片尺寸过小，请上传尺寸大于 128px 的图片')
          setTimeout(() => {
            setLoading(false)
          }, 1000)

          if (fileInputRef.current) {
            fileInputRef.current.value = ''
          }
          return
        }

        if (Math.max(fileInfo.width, fileInfo.height) > 4096) {
          toast.error('图片尺寸过大，请上传尺寸小于 4096px 的图片')
          setTimeout(() => {
            setLoading(false)
          }, 1000)

          if (fileInputRef.current) {
            fileInputRef.current.value = ''
          }
          return
        }
        const data = await uploadImage(file)

        if (data) {
          onSuccess?.(data)
        }

        if (fileInfo.base64 && fileInputRef.current) {
          fileInputRef.current.value = ''
        }
      } catch (error) {
        console.error(error)
        toast.error('上传失败')
      } finally {
        setLoading(false)
      }
    }
  }

  const handleButtonClick = () => {
    fileInputRef.current?.click()
  }
  return (
    <div className={className}>
      <Label className="sr-only">{text}</Label>
      <Input
        ref={fileInputRef}
        id="picture"
        type="file"
        accept="image/jpeg, image/png, image/webp"
        className="hidden"
        disabled={loading}
        onChange={handleFileChange}
      />
      <Button
        onClick={handleButtonClick}
        disabled={loading}
        className="w-full"
        size={size}
      >
        {loading ? (
          <Loader2 className="size-4 animate-spin" />
        ) : (
          <ImagePlus className="size-4" />
        )}
        {loading ? '上传中...' : text}
      </Button>
    </div>
  )
}
