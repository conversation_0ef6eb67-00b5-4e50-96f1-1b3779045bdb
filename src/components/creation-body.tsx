import { useDebounceEffect, useScroll } from 'ahooks'
import { Palette } from 'lucide-react'
import { useCallback, useEffect, useMemo, useRef, useState } from 'react'
import { useNavigationType } from 'react-router'
import { useCreationStore } from '@/stores/creation'
import CreationBox from './creation-box'
import CreationInput from './creation-input'
import { CreationTypeToggle } from './creation-type-toggle'

export default function CreationBody() {
  const navigationType = useNavigationType()
  const scroll = useScroll(document.getElementById('creation-scroll-container'))
  const scrollContainerRef = useRef<HTMLDivElement>(null)
  const isInitialLoadRef = useRef(true)
  const previousJobListLengthRef = useRef(0)
  const isLoadingHistoryRef = useRef(false)
  const scrollPositionBeforeLoadRef = useRef<{
    scrollTop: number
    scrollHeight: number
  } | null>(null)
  const [tempHeight, setTempHeight] = useState<number | null>(null)
  const { jobList, serverPullJobTotal, jobTotal, getJobHistoryFn } =
    useCreationStore()

  const hasJob = useMemo(() => jobList.length > 0, [jobList])
  const hasOtherJob = useMemo(
    () => serverPullJobTotal < jobTotal,
    [serverPullJobTotal, jobTotal]
  )

  // 滚动到底部的函数
  const scrollToBottom = useCallback(() => {
    const container = scrollContainerRef.current
    if (container) {
      container.scrollTop = container.scrollHeight
    }
  }, [])

  // 保持当前视口位置的滚动恢复函数
  const preserveScrollPosition = useCallback(() => {
    const container = scrollContainerRef.current
    const savedPosition = scrollPositionBeforeLoadRef.current

    if (container && savedPosition) {
      const newScrollHeight = container.scrollHeight
      const scrollHeightDiff = newScrollHeight - savedPosition.scrollHeight

      // 立即调整滚动位置
      container.scrollTop = savedPosition.scrollTop + scrollHeightDiff

      // 清理状态
      scrollPositionBeforeLoadRef.current = null
      setTempHeight(null)
    }
  }, [])

  const handlePullJobList = useCallback(() => {
    // 向上滚动时，距离顶部 200px 时触发加载
    if (
      scroll &&
      scroll.top <= 200 &&
      hasOtherJob &&
      !isLoadingHistoryRef.current
    ) {
      console.info('handlePullJobList - loading more history')
      const container = scrollContainerRef.current

      if (container) {
        // 标记正在加载历史数据
        isLoadingHistoryRef.current = true

        // 固定容器高度防止闪烁
        setTempHeight(container.scrollHeight)

        // 保存当前滚动位置信息
        scrollPositionBeforeLoadRef.current = {
          scrollTop: container.scrollTop,
          scrollHeight: container.scrollHeight,
        }

        // 加载历史数据
        getJobHistoryFn(jobList[0]?.incr_id)
      }
    }

    // 首次加载数据时
    if (jobList.length === 0) {
      getJobHistoryFn()
    }
  }, [getJobHistoryFn, jobList, scroll, hasOtherJob])
  // 首次进入页面时滚动到底部显示最新数据
  useEffect(() => {
    if (isInitialLoadRef.current && hasJob) {
      const container = scrollContainerRef.current
      const saved = localStorage.getItem('creation-scroll-position')

      if (navigationType === 'POP' && saved) {
        setTimeout(() => {
          if (container) {
            container.scrollTo(0, parseInt(saved || '0'))
          }
          isInitialLoadRef.current = false
        }, 100)
      } else {
        setTimeout(() => {
          scrollToBottom()
          isInitialLoadRef.current = false
        }, 100)
      }
    }
  }, [hasJob, scrollToBottom, navigationType])

  // 监听 jobList 变化，处理历史数据加载后的滚动位置恢复
  useEffect(() => {
    const currentLength = jobList.length
    const previousLength = previousJobListLengthRef.current

    // 如果是历史数据加载（长度增加且正在加载历史）
    if (isLoadingHistoryRef.current && currentLength > previousLength) {
      // 等待下一个渲染帧后恢复位置
      requestAnimationFrame(() => {
        preserveScrollPosition()
        isLoadingHistoryRef.current = false
      })
    }
    // 如果是新增任务（而不是历史数据加载），则滚动到底部
    else if (
      !isInitialLoadRef.current &&
      !isLoadingHistoryRef.current &&
      currentLength > previousLength &&
      currentLength > 0
    ) {
      // 判断是否是在底部附近（距离底部 100px 以内），如果是则自动滚动
      const container = scrollContainerRef.current
      if (container) {
        const isNearBottom =
          container.scrollHeight -
            container.scrollTop -
            container.clientHeight <=
          100
        if (isNearBottom) {
          requestAnimationFrame(() => {
            scrollToBottom()
          })
        }
      }
    }

    previousJobListLengthRef.current = currentLength
  }, [jobList.length, scrollToBottom, preserveScrollPosition])
  // useUpdateLayoutEffect(() => {
  //   if (jobLength > 0) {
  //     scrollToBottom();
  //   }
  // }, [jobLength]);
  useDebounceEffect(
    () => {
      handlePullJobList()
    },
    [scroll],
    {
      wait: 100,
    }
  )

  return (
    <div className="flex-1 h-screen relative flex flex-col">
      <div
        ref={scrollContainerRef}
        className="h-full flex-1 overflow-y-auto"
        id="creation-scroll-container"
        style={{
          scrollBehavior: 'auto',
        }}
      >
        {hasJob ? (
          <div
            className="p-4 max-w-[1536px] w-full mx-auto"
            style={{
              minHeight: tempHeight ? `${tempHeight}px` : 'auto',
            }}
          >
            {hasOtherJob && (
              <p className="text-xs text-muted-foreground text-center pb-3 opacity-50">
                向上滑动查看更多历史记录...
              </p>
            )}
            {jobList.map((item, index) => (
              <CreationBox key={`${item.id}-${index}`} job={item} />
            ))}
            <p className="text-xs text-muted-foreground text-center pt-3 opacity-50">
              已经是最新的了...
            </p>
            <div
              className="h-1 w-full opacity-0 mt-4"
              id="creation-scroll-bottom"
            />
          </div>
        ) : (
          <div className="h-full flex flex-col justify-center items-center gap-3">
            <h2 className="text-xl font-bold">
              <Palette className="inline-block mr-2 vertical-middle size-6 -mt-1" />
              开始您的创意之旅
            </h2>
            <p className="text-sm text-muted-foreground">
              在下方输入框中输入文字，按回车键开始生成图片
            </p>
          </div>
        )}
      </div>
      <div className="max-w-[1536px] w-full mx-auto relative px-4 py-2 xl:py-4 z-10 flex flex-col items-start">
        <CreationTypeToggle />
        <CreationInput />
      </div>
    </div>
  )
}
