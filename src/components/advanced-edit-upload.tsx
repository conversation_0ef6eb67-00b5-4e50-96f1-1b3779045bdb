import { ArrowUp<PERSON><PERSON><PERSON><PERSON>, <PERSON> } from 'lucide-react'
import { useCallback, useState } from 'react'
import { toast } from 'sonner'
import { cn, uploadImageByUrl } from '@/lib/utils'
import type { UploadDataType } from '@/stores/advanced-edit'
import { Button } from './ui/button'
import { Input } from './ui/input'
import Upload from './upload-real'

type AdvancedEditUploadProps = {
  className?: string
  onSuccess?: (data: UploadDataType) => void
}

export default function AdvancedEditUpload({
  className,
  onSuccess,
}: AdvancedEditUploadProps) {
  const [url, setUrl] = useState('')
  const handleImport = useCallback(async () => {
    if (url) {
      const data = await uploadImageByUrl(url)

      if (data) {
        onSuccess?.(data)
      }
    } else {
      toast.error('请输入图片地址')
    }
  }, [url, onSuccess])

  return (
    <div className={cn('flex flex-col gap-3', className)}>
      {/* URL 导入区域 */}
      <div className="border w-full z-10 p-6 rounded-xl bg-background/50 backdrop-blur-sm flex flex-col justify-center items-center gap-3 hover:bg-background/80 transition-colors duration-200">
        <div className="flex items-center gap-2.5">
          <Link className="w-4 h-4 text-muted-foreground" />
          <h2 className="text-lg font-semibold text-foreground">
            从图片地址导入
          </h2>
        </div>
        <p className="text-sm text-muted-foreground text-center max-w-md">
          粘贴图片链接即可快速开始编辑
        </p>
        <div className="w-full max-w-md">
          <form
            onSubmit={e => {
              e.preventDefault()
              handleImport()
            }}
            className="flex w-full items-center gap-2"
          >
            <Input
              type="url"
              placeholder="https://example.com/image.jpg"
              className="flex-1 h-9 text-sm"
              value={url}
              onChange={e => setUrl(e.target.value)}
            />
            <Button
              type="submit"
              disabled={!url}
              onClick={handleImport}
              size="sm"
              className="h-9 px-3"
            >
              导入
            </Button>
          </form>
        </div>
      </div>

      {/* 本地上传区域 */}
      <div className="border w-full z-10 p-6 rounded-xl bg-background/50 backdrop-blur-sm flex flex-col justify-center items-center gap-3 hover:bg-background/80 transition-colors duration-200">
        <div className="flex items-center gap-2.5">
          <ArrowUpFromLine className="w-4 h-4 text-muted-foreground" />
          <h2 className="text-lg font-semibold text-foreground">
            从本地选择导入
          </h2>
        </div>
        <p className="text-sm text-muted-foreground text-center max-w-md">
          选择本地图片文件开始编辑之旅
        </p>
        <div className="w-full max-w-md">
          <Upload
            onSuccess={onSuccess}
            className="w-full"
            text="选择图片文件"
          />
        </div>
      </div>
    </div>
  )
}
