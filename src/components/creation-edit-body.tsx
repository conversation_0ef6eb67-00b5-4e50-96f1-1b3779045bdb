import { saveAs } from 'file-saver'
import {
  <PERSON><PERSON><PERSON>,
  ArrowLeft,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON>p,
  Clock10,
  Download,
  X,
} from 'lucide-react'
import { useCallback, useMemo } from 'react'
import Markdown from 'react-markdown'
import { useNavigate } from 'react-router'
import { toast } from 'sonner'
import {
  CrossOriginDownloader,
  cn,
  getJobTypeClass,
  PromptCommandParser,
} from '@/lib/utils'
import {
  DiffusionStatus,
  isArticleBasemap,
  type JobItemType,
  JobType,
  type motionType,
  useCreationStore,
  type VideoGenerationParameter,
} from '@/stores/creation'
import AdvancedGoButton from './advanced-go-btn'
import Basemap from './basemap'
import CopyrightTag from './copyright-tag'
import CreationInput from './creation-input'
import CreationPrompt from './creation-prompt'
import { CreationTypeToggle } from './creation-type-toggle'
import { Badge } from './ui/badge'
import { Button } from './ui/button'

type CreationEditBodyProps = {
  job: JobItemType
  index: number
}
export default function CreationEditBody({
  job,
  index = 0,
}: CreationEditBodyProps) {
  const navigate = useNavigate()
  const {
    setPrompt,
    jobReroll,
    addImageBasemap,
    variation,
    upscale,
    pan,
    outpaint,
    removeBackground,
    setImageRemixBasemap,
    videoUpscale,
    autoExtendVideo,
    setVideoExtend,
    setVideoFirstFrame,
    setArticle,
    generationType: generationTypeStore,
    setGenerationType,
  } = useCreationStore()
  const {
    id: job_id,
    text,
    type,
    created_at,
    urls = [],
    status,
    is_copyright = 0,
    basemap = [],
    audits = [],
    article = '',
  } = job

  const loading = useMemo(
    () =>
      !(
        status === DiffusionStatus.COMPLETED ||
        status === DiffusionStatus.FAILED
      ),
    [status]
  )
  const image = useMemo(() => {
    const obj = urls[index]

    if (obj) {
      return obj.webp || obj.url
    }

    return undefined
  }, [urls, index])
  const { data } = useMemo(() => PromptCommandParser.parse(text), [text])
  const { width, height } = useMemo(
    () => data?.aspectRatio || { width: 1, height: 1 },
    [data]
  )
  const generationType = useMemo(() => {
    return getJobTypeClass(type)
  }, [type])
  const prompt = useMemo(() => {
    return generationType === 'video'
      ? data?.prompt || ''
      : data?.prompt || text
  }, [data, text, generationType])
  const isBad = useMemo(
    () =>
      !!audits[index] &&
      !image &&
      (generationType === 'image' || generationType === 'video'),
    [image, audits, index, generationType]
  )
  const videoFirstFrame = useMemo(
    () =>
      basemap.find(({ type }) => type === 'video_first_frame') as
        | VideoGenerationParameter
        | undefined,
    [basemap]
  )
  const articleBasemap = useMemo(
    () => basemap.find(isArticleBasemap),
    [basemap]
  )
  const handleDownload = useCallback(async () => {
    if (!image && type !== JobType.ARTICLE_GENERATION) {
      toast.error('未找到相应的资源')
      return
    }

    if (image) {
      CrossOriginDownloader.download(image)
      toast.info('资源正在下载，稍等几秒后在浏览器下载管理查看')
    } else {
      const blob = new Blob([article], { type: 'text/plain' }) // txt to pdf
      saveAs(blob, `${job_id}-article.txt`)
      toast.success('文章下载成功')
    }
  }, [image, type, article, job_id])
  const handleVideoExtend = useCallback(
    (motion: motionType) => {
      if (!image) {
        toast.error('未找到相应的资源')
        return
      }

      if (generationTypeStore !== 'video') {
        setGenerationType('video')
      }
      setVideoExtend({
        type: 'video_extend',
        jobId: job_id,
        videoNo: index,
        motion,
        url: image,
        is_copyright,
      })
      setPrompt(`${prompt} --motion ${motion}`)
      toast.success('请点击“生成视频”按钮进行视频延长')
    },
    [
      image,
      generationTypeStore,
      setVideoExtend,
      job_id,
      index,
      is_copyright,
      setPrompt,
      prompt,
      setGenerationType,
    ]
  )
  const handleGoBack = useCallback(() => {
    navigate(-1)
  }, [navigate])

  if (!image && type !== JobType.ARTICLE_GENERATION) {
    return (
      <div className="flex-1 relative bg-muted h-full p-4 pb-28 overflow-hidden flex justify-center items-center">
        <p className="text-muted-foreground">未找到相应的资源</p>
      </div>
    )
  }

  return (
    <div className="relative h-full flex-1 min-h-96 overflow-hidden flex">
      <div
        className={cn(
          'flex-1 relative bg-muted h-full p-4 pb-44 overflow-hidden flex justify-center items-center',
          {
            // "items-start": generationType === 'article',
          }
        )}
      >
        {generationType === 'image' && image && (
          <img
            src={`${image}?x-oss-process=image/format,webp`}
            alt="basic"
            className={cn(
              'select-none contain-layout block max-w-full max-h-full rounded-lg shadow-lg bg-[length:16px_16px] bg-white-box dark:bg-black-box min-h-32 min-w-32',
              {
                'animate-pulse': loading,
              }
            )}
            draggable={false}
            loading="lazy"
            style={{
              viewTransitionName: `creation-image-${index}`,
            }}
          />
        )}
        {generationType === 'video' && image && (
          <video
            src={image}
            className="select-none contain-layout block max-w-full max-h-full rounded-lg shadow-lg bg-muted-foreground/10 min-h-32 min-w-32"
            autoPlay
            muted
            loop
            preload="auto"
            style={{
              viewTransitionName: `creation-video-${index}`,
            }}
          />
        )}
        {isBad && (
          <p className="absolute w-3/5 top-1/2 left-1/2 -translate-x-1/2 -translate-y-1/2 bg-muted border p-2 text-center rounded text-sm">
            此素材的脑洞已经突破了我的想象力结界
          </p>
        )}
        {generationType === 'article' && (
          <article
            className="prose max-w-full max-h-full prose-sm dark:prose-invert overflow-hidden overflow-y-auto scrollbar-thin scrollbar-thumb-muted-foreground/50 scrollbar-track-transparent p-2 bg-muted w-full rounded-md relative"
            style={
              {
                // viewTransitionName: `creation-article-${job_id}`,
              }
            }
          >
            <Markdown>{article}</Markdown>
          </article>
        )}
        <div className="max-w-[1536px] w-full mx-auto absolute bottom-4 left-1/2 -translate-x-1/2 px-4 z-10 flex flex-col items-start">
          <CreationTypeToggle />
          <CreationInput />
        </div>
      </div>
      <div className="w-96 p-4 overflow-hidden h-full flex flex-col">
        <div className="flex flex-col gap-1 xl:gap-2">
          <div className="flex items-center gap-2">
            {type !== JobType.REMOVE_BACKGROUND &&
              type !== JobType.ARTICLE_GENERATION && (
                <div className="mr-auto">
                  <CopyrightTag is_copyright={is_copyright} />
                </div>
              )}
            <Button
              variant="default"
              size="icon"
              disabled={loading || isBad}
              className="ml-auto"
              onClick={handleDownload}
            >
              <Download className="size-4" />
            </Button>
            <Button variant="outline" size="icon" onClick={handleGoBack}>
              <X className="size-4" />
            </Button>
          </div>
          <div
            className="flex flex-col items-start gap-1"
            style={
              {
                // viewTransitionName: `creation-image-info-${job_id}`,
              }
            }
          >
            <CreationPrompt prompt={prompt} setPrompt={setPrompt} type={type} />
            {created_at && (
              <Badge
                variant="outline"
                className="text-muted-foreground font-light"
              >
                <Clock10 className="size-3 mr-1" />
                {created_at}
              </Badge>
            )}
            <Basemap
              className="-ml-2"
              basemap={basemap}
              boxType={generationType}
            />
          </div>
        </div>
        <div className="mt-auto">
          <p className="pt-5 pb-3 text-sm text-muted-foreground font-medium">
            创作选项
          </p>
          {generationType === 'image' && (
            <>
              {type !== JobType.REMOVE_BACKGROUND && (
                <div className="flex items-center justify-between py-2">
                  <p className="text-xs text-muted-foreground font-medium">
                    变化
                  </p>
                  <div className="flex gap-1 w-60">
                    <Button
                      variant="secondary"
                      size="sm"
                      className="flex-1 text-muted-foreground"
                      disabled={loading}
                      onClick={() => {
                        variation(job_id, index, 0, handleGoBack)
                      }}
                    >
                      细微
                    </Button>
                    <Button
                      variant="secondary"
                      size="sm"
                      className="flex-1 text-muted-foreground"
                      disabled={loading}
                      onClick={() => {
                        variation(job_id, index, 1, handleGoBack)
                      }}
                    >
                      强烈
                    </Button>
                  </div>
                </div>
              )}
              {!(
                type === JobType.UPSCALE || type === JobType.REMOVE_BACKGROUND
              ) && (
                <div className="flex items-center justify-between py-2">
                  <p className="text-xs text-muted-foreground font-medium">
                    高清
                  </p>
                  <div className="flex gap-1 w-60">
                    <Button
                      variant="secondary"
                      size="sm"
                      className="flex-1 text-muted-foreground"
                      disabled={loading}
                      onClick={() => {
                        upscale(job_id, index, 0, handleGoBack)
                      }}
                    >
                      直接
                    </Button>
                    <Button
                      variant="secondary"
                      size="sm"
                      className="flex-1 text-muted-foreground"
                      disabled={loading}
                      onClick={() => {
                        upscale(job_id, index, 1, handleGoBack)
                      }}
                    >
                      创意
                    </Button>
                  </div>
                </div>
              )}
              {type !== JobType.REMOVE_BACKGROUND && (
                <div className="flex items-center justify-between py-2">
                  <p className="text-xs text-muted-foreground font-medium">
                    重塑
                  </p>
                  <div className="flex gap-1 w-60">
                    <Button
                      variant="secondary"
                      size="sm"
                      className="flex-1 text-muted-foreground"
                      disabled={loading}
                      onClick={() => {
                        setImageRemixBasemap(job, index, 1)
                      }}
                    >
                      细微
                    </Button>
                    <Button
                      variant="secondary"
                      size="sm"
                      className="flex-1 text-muted-foreground"
                      disabled={loading}
                      onClick={() => {
                        setImageRemixBasemap(job, index, 0)
                      }}
                    >
                      强烈
                    </Button>
                  </div>
                </div>
              )}
              {!(
                type === JobType.UPSCALE || type === JobType.REMOVE_BACKGROUND
              ) && (
                <div className="flex items-center justify-between py-2">
                  <p className="text-xs text-muted-foreground font-medium">
                    延展
                  </p>
                  <div className="flex gap-1 w-60">
                    <Button
                      variant="secondary"
                      size="sm"
                      className="flex-1 text-muted-foreground"
                      disabled={loading}
                      onClick={() => {
                        pan(job_id, index, 2, 1.2, handleGoBack)
                      }}
                    >
                      <ArrowUp className="size-3" />
                    </Button>
                    <Button
                      variant="secondary"
                      size="sm"
                      className="flex-1 text-muted-foreground"
                      disabled={loading}
                      onClick={() => {
                        pan(job_id, index, 0, 1.2, handleGoBack)
                      }}
                    >
                      <ArrowDown className="size-3" />
                    </Button>
                    <Button
                      variant="secondary"
                      size="sm"
                      className="flex-1 text-muted-foreground"
                      disabled={loading}
                      onClick={() => {
                        pan(job_id, index, 3, 1.2, handleGoBack)
                      }}
                    >
                      <ArrowLeft className="size-3" />
                    </Button>
                    <Button
                      variant="secondary"
                      size="sm"
                      className="flex-1 text-muted-foreground"
                      disabled={loading}
                      onClick={() => {
                        pan(job_id, index, 1, 1.2, handleGoBack)
                      }}
                    >
                      <ArrowRight className="size-3" />
                    </Button>
                  </div>
                </div>
              )}
              {type !== JobType.REMOVE_BACKGROUND && (
                <div className="flex items-center justify-between py-2">
                  <p className="text-xs text-muted-foreground font-medium">
                    缩放
                  </p>
                  <div className="flex gap-1 w-60">
                    <Button
                      variant="secondary"
                      size="sm"
                      className="flex-1 text-muted-foreground"
                      disabled={loading}
                      onClick={() => {
                        outpaint(job_id, index, 1.5, handleGoBack)
                      }}
                    >
                      1.5x
                    </Button>
                    <Button
                      variant="secondary"
                      size="sm"
                      className="flex-1 text-muted-foreground"
                      disabled={loading}
                      onClick={() => {
                        outpaint(job_id, index, 2, handleGoBack)
                      }}
                    >
                      2x
                    </Button>
                  </div>
                </div>
              )}
              <div className="flex items-center justify-between py-2">
                <p className="text-xs text-muted-foreground font-medium">
                  编辑
                </p>
                <div className="flex gap-1 w-60">
                  <AdvancedGoButton
                    loading={loading}
                    image={urls[index]}
                    className="flex-1 text-muted-foreground"
                  />
                  {type !== JobType.UPSCALE &&
                    type !== JobType.REMOVE_BACKGROUND &&
                    image && (
                      <Button
                        variant="secondary"
                        size="sm"
                        className="flex-1 text-muted-foreground"
                        disabled={loading}
                        onClick={() => {
                          removeBackground(image, job_id, handleGoBack)
                        }}
                      >
                        去除背景
                      </Button>
                    )}
                </div>
              </div>
            </>
          )}
          {(type === JobType.ARTICLE_GENERATION ||
            type === JobType.DEFAULT) && (
            <div className="flex items-center justify-between py-2">
              <p className="text-xs text-muted-foreground font-medium">更多</p>
              <div className="flex gap-1 w-60">
                {type === JobType.ARTICLE_GENERATION && (
                  <>
                    <Button
                      variant="secondary"
                      size="sm"
                      className="flex-1 text-muted-foreground"
                      disabled={loading}
                      onClick={() => {
                        navigator.clipboard.writeText(article)
                        toast.success('已复制文章')
                      }}
                    >
                      复制文章
                    </Button>
                    <Button
                      variant="secondary"
                      size="sm"
                      className="flex-1 text-muted-foreground"
                      // disabled={loading}
                      disabled={true}
                      onClick={() => {
                        // setPrompt(prompt);
                      }}
                    >
                      编辑文章
                    </Button>
                  </>
                )}
                {/* {status !== DiffusionStatus.FAILED &&
                  generationType !== 'video' && (
                    <Button
                      variant="secondary"
                      size="sm"
                      className="flex-1 text-muted-foreground"
                      disabled={loading}
                      onClick={() => {
                        jobReroll(job_id, handleGoBack)
                      }}
                    >
                      重新生成
                    </Button>
                  )} */}
                {type === JobType.DEFAULT && (
                  <Button
                    variant="secondary"
                    size="sm"
                    className="flex-1 text-muted-foreground"
                    disabled={loading}
                    onClick={() => {
                      jobReroll(job_id, handleGoBack)
                    }}
                  >
                    重新生成
                  </Button>
                )}
              </div>
            </div>
          )}
          <div className="flex items-center justify-between py-2">
            <p className="text-xs text-muted-foreground font-medium">使用</p>
            <div className="flex gap-1 w-60">
              {generationType === 'video' && videoFirstFrame && (
                <Button
                  variant="secondary"
                  size="sm"
                  className="flex-1 text-muted-foreground"
                  onClick={() => setVideoFirstFrame(videoFirstFrame)}
                >
                  首帧
                </Button>
              )}
              {generationType === 'image' && image && (
                <>
                  <Button
                    variant="secondary"
                    size="sm"
                    className="flex-1 text-muted-foreground"
                    onClick={() =>
                      addImageBasemap([
                        {
                          url: image,
                          type: 'content',
                          jobId: job_id,
                          imageNo: index,
                          width,
                          height,
                          is_copyright,
                        },
                      ])
                    }
                  >
                    内容
                  </Button>
                  <Button
                    variant="secondary"
                    size="sm"
                    className="flex-1 text-muted-foreground"
                    onClick={() =>
                      addImageBasemap([
                        {
                          url: image,
                          type: 'sref',
                          jobId: job_id,
                          imageNo: index,
                          width,
                          height,
                          is_copyright,
                        },
                      ])
                    }
                  >
                    风格
                  </Button>
                  <Button
                    variant="secondary"
                    size="sm"
                    className="flex-1 text-muted-foreground"
                    onClick={() =>
                      addImageBasemap([
                        {
                          url: image,
                          type: 'cref',
                          jobId: job_id,
                          imageNo: index,
                          width,
                          height,
                          is_copyright,
                        },
                      ])
                    }
                  >
                    人脸
                  </Button>
                </>
              )}
              {type === JobType.ARTICLE_GENERATION && articleBasemap && (
                <>
                  <Button
                    variant="secondary"
                    size="sm"
                    className="flex-1 text-muted-foreground"
                    onClick={() => setArticle({ class: articleBasemap.class })}
                  >
                    类型
                  </Button>
                  <Button
                    variant="secondary"
                    size="sm"
                    className="flex-1 text-muted-foreground"
                    onClick={() =>
                      setArticle({ lenght: articleBasemap.lenght })
                    }
                  >
                    长度
                  </Button>
                  <Button
                    variant="secondary"
                    size="sm"
                    className="flex-1 text-muted-foreground"
                    onClick={() => setArticle({ tone: articleBasemap.tone })}
                  >
                    语气
                  </Button>
                </>
              )}
              {prompt && (
                <Button
                  variant="secondary"
                  size="sm"
                  className="flex-1 text-muted-foreground"
                  onClick={() => setPrompt(prompt)}
                >
                  提示词
                </Button>
              )}
            </div>
          </div>
          {generationType === 'video' && (
            <>
              <div className="flex items-center justify-between py-2">
                <p className="text-xs text-muted-foreground font-medium">
                  清晰度
                </p>
                <div className="flex gap-1 w-60">
                  <Button
                    variant="secondary"
                    size="sm"
                    className="flex-1 text-muted-foreground"
                    disabled={loading || type === JobType.VIDEO_UPSCALE}
                    onClick={() => {
                      videoUpscale(job_id, index, handleGoBack)
                    }}
                  >
                    高清
                  </Button>
                </div>
              </div>
              <p className="pt-5 pb-3 text-sm text-muted-foreground font-medium">
                延长视频
              </p>
              <div className="flex items-center justify-between py-2">
                <p className="text-xs text-muted-foreground font-medium">
                  自动
                </p>
                <div className="flex gap-1 w-60">
                  <Button
                    variant="secondary"
                    size="sm"
                    className="flex-1 text-muted-foreground"
                    disabled={loading || type === JobType.VIDEO_UPSCALE}
                    onClick={() => {
                      autoExtendVideo(job_id, index, 'low', handleGoBack)
                    }}
                  >
                    低速运动
                  </Button>
                  <Button
                    variant="secondary"
                    size="sm"
                    className="flex-1 text-muted-foreground"
                    disabled={loading || type === JobType.VIDEO_UPSCALE}
                    onClick={() => {
                      autoExtendVideo(job_id, index, 'high', handleGoBack)
                    }}
                  >
                    高速运动
                  </Button>
                </div>
              </div>
              <div className="flex items-center justify-between py-2">
                <p className="text-xs text-muted-foreground font-medium">
                  手动
                </p>
                <div className="flex gap-1 w-60">
                  <Button
                    variant="secondary"
                    size="sm"
                    className="flex-1 text-muted-foreground"
                    disabled={loading || type === JobType.VIDEO_UPSCALE}
                    onClick={() => handleVideoExtend('low')}
                  >
                    低速运动
                  </Button>
                  <Button
                    variant="secondary"
                    size="sm"
                    className="flex-1 text-muted-foreground"
                    disabled={loading || type === JobType.VIDEO_UPSCALE}
                    onClick={() => handleVideoExtend('high')}
                  >
                    高速运动
                  </Button>
                </div>
              </div>
            </>
          )}
        </div>
      </div>
    </div>
  )
}
