import { useDebounceEffect, useDebounceFn } from 'ahooks'
import type React from 'react'
import { useCallback, useEffect, useMemo, useRef, useState } from 'react'
import { cn } from '@/lib/utils'
import type { VCGImageType } from '@/stores/creation'
import SearchBox from './advanced-search-box'

export interface MasonryGridProps {
  items: VCGImageType[]
  responsiveColumns?: { [key: string]: number }
  gap?: number
  itemClassName?: string
  containerClassName?: string
  renderItem?: (item: VCGImageType, index: number) => React.ReactNode
}

interface LayoutItem {
  id: number
  width: number
  height: number
  top: number
  left: number
  index: number
  data: VCGImageType
}

const DEFAULT_COLUMNS = {
  default: 4,
  1680: 6,
  1380: 5,
  1080: 4,
  768: 3,
  350: 2,
  0: 1,
}

export default function MasonryGrid({
  items = [],
  responsiveColumns = DEFAULT_COLUMNS,
  gap = 8,
  itemClassName,
  containerClassName,
  renderItem,
}: MasonryGridProps) {
  const [containerWidth, setContainerWidth] = useState(0)
  const [columns, setColumns] = useState(responsiveColumns.default || 4)
  const [layoutItems, setLayoutItems] = useState<LayoutItem[]>([])
  const containerRef = useRef<HTMLDivElement>(null)
  const resizeObserverRef = useRef<ResizeObserver | null>(null)

  // 获取当前列数
  const getCurrentColumns = useCallback(() => {
    if (!containerWidth) return responsiveColumns.default || 4

    const sortedBreakpoints = Object.entries(responsiveColumns)
      .filter(([key]) => key !== 'default')
      .sort(([a], [b]) => parseInt(b) - parseInt(a))

    for (const [breakpoint, cols] of sortedBreakpoints) {
      if (containerWidth >= parseInt(breakpoint)) {
        return cols
      }
    }

    return responsiveColumns.default || 4
  }, [containerWidth, responsiveColumns])

  // 计算布局
  const calculateLayout = useCallback(() => {
    if (items.length === 0) {
      setLayoutItems([])
      return
    }

    // 如果容器宽度为 0，使用默认宽度进行估算
    const effectiveWidth = containerWidth || 1200

    const currentColumns = getCurrentColumns()
    const columnWidth =
      (effectiveWidth - (currentColumns - 1) * gap) / currentColumns

    // 初始化列高度数组
    const columnHeights = new Array(currentColumns).fill(0)

    // 计算每个项目的位置
    const newLayoutItems: LayoutItem[] = items.map((item, index) => {
      // 找到最短的列
      const shortestColumnIndex = columnHeights.indexOf(
        Math.min(...columnHeights)
      )

      // 计算项目位置
      const left = shortestColumnIndex * (columnWidth + gap)
      const top = columnHeights[shortestColumnIndex]

      const { height, width, id } = item

      // 计算项目的显示高度（保持宽高比）
      const aspectRatio = height / width
      const displayHeight = Math.max(columnWidth * aspectRatio, 128)

      // 更新列高度
      columnHeights[shortestColumnIndex] += displayHeight + gap

      return {
        id,
        width: columnWidth,
        height: displayHeight,
        top,
        left,
        index,
        data: item,
      }
    })

    setLayoutItems(newLayoutItems)
  }, [containerWidth, items, gap, getCurrentColumns])

  // 更新容器宽度的防抖函数
  const { run: debouncedUpdateWidth } = useDebounceFn(
    () => {
      if (containerRef.current) {
        setContainerWidth(containerRef.current.offsetWidth)
      }
    },
    { wait: 100 }
  )

  // 监听容器大小变化
  useEffect(() => {
    if (!containerRef.current) return

    const updateWidth = () => {
      if (containerRef.current) {
        setContainerWidth(containerRef.current.offsetWidth)
      }
    }

    // 初始设置
    updateWidth()

    // 创建 ResizeObserver 监听容器大小变化
    if (typeof ResizeObserver !== 'undefined') {
      const resizeObserver = new ResizeObserver(() => {
        debouncedUpdateWidth()
      })

      resizeObserver.observe(containerRef.current)
      resizeObserverRef.current = resizeObserver
    }

    // 监听窗口大小变化
    window.addEventListener('resize', debouncedUpdateWidth)

    return () => {
      window.removeEventListener('resize', debouncedUpdateWidth)
      if (resizeObserverRef.current) {
        resizeObserverRef.current.disconnect()
      }
    }
  }, [debouncedUpdateWidth])

  // 当容器宽度或列数变化时重新计算布局
  useEffect(() => {
    const newColumns = getCurrentColumns()
    if (newColumns !== columns) {
      setColumns(newColumns)
    }
  }, [getCurrentColumns, columns])

  // 当数据或列数变化时重新计算布局
  useDebounceEffect(
    () => {
      calculateLayout()
    },
    [
      columns,
      items.length,
      items.map(item => item.id).join(','),
      containerWidth,
      getCurrentColumns,
    ],
    { wait: 100 }
  )

  // 计算容器总高度
  const containerHeight = useMemo(() => {
    if (layoutItems.length === 0) return 0
    return Math.max(...layoutItems.map(item => item.top + item.height))
  }, [layoutItems])

  // 默认渲染函数使用 SearchBox 组件
  const defaultRenderItem = useCallback((item: VCGImageType) => {
    return <SearchBox key={item.id} result={item} />
  }, [])

  const renderFunction = renderItem || defaultRenderItem

  return (
    <div
      ref={containerRef}
      className={cn('relative w-full', containerClassName)}
      style={{ height: containerHeight }}
    >
      {layoutItems.map(layoutItem => (
        <div
          key={layoutItem.id}
          className={cn(
            'absolute transition-all duration-300 min-h-32 bg-muted-foreground/10 rounded',
            itemClassName
          )}
          style={{
            top: `${layoutItem.top}px`,
            left: `${layoutItem.left}px`,
            width: `${layoutItem.width}px`,
            height: `${layoutItem.height}px`,
          }}
        >
          {renderFunction(layoutItem.data, layoutItem.index)}
        </div>
      ))}
    </div>
  )
}
