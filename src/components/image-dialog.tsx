import { X } from 'lucide-react'
import { useImageStore } from '@/stores/image'
import { Button } from './ui/button'
import {
  Dialog,
  DialogClose,
  DialogContent,
  DialogDescription,
  DialogTitle,
} from './ui/dialog'

export default function ImageDialog() {
  const { imageUrl, setImageUrl } = useImageStore()

  return (
    <Dialog open={!!imageUrl} onOpenChange={() => setImageUrl(null)}>
      <DialogContent className="max-w-full max-h-full w-full h-full flex justify-center items-center pl-12 bg-black/20 border-none">
        <DialogTitle className="sr-only">Image Preview</DialogTitle>
        <DialogDescription className="sr-only">
          Image preview dialog
        </DialogDescription>
        <DialogClose asChild>
          <Button
            variant="outline"
            size="icon"
            className="absolute top-2 right-2 z-50"
            onClick={() => setImageUrl(null)}
          >
            <X className="size-4" />
          </Button>
        </DialogClose>
        <img
          src={imageUrl || ''}
          alt={imageUrl || ''}
          className="relative z-10"
        />
        <button
          type="button"
          onClick={() => setImageUrl(null)}
          className="w-full h-full absolute"
        />
      </DialogContent>
    </Dialog>
  )
}
