import { useEffect, useRef } from 'react'

interface CustomCursorProps {
  visible: boolean
  brushSize: number
  x: number
  y: number
  operationType?: 'erase' | 'reverseErase' | 'move'
}

export default function CustomCursor({
  visible,
  brushSize,
  x,
  y,
  operationType,
}: CustomCursorProps) {
  const cursorRef = useRef<HTMLDivElement>(null)

  useEffect(() => {
    if (cursorRef.current) {
      cursorRef.current.style.left = `${x}px`
      cursorRef.current.style.top = `${y}px`
    }
  }, [x, y])

  if (!visible) return null

  const isReverseErase = operationType === 'reverseErase'

  return (
    <div
      ref={cursorRef}
      className="fixed pointer-events-none z-30 transform -translate-x-1/2 -translate-y-1/2"
      style={{
        width: `${brushSize * 2}px`,
        height: `${brushSize * 2}px`,
      }}
    >
      {/* 外圆 */}
      <div
        className="absolute inset-0 rounded-full border-2 border-white bg-foreground/30"
        style={{
          boxShadow: '0 0 0 1px rgba(0, 0, 0, 0.5)',
        }}
      />
      {/* 中心符号 - 减号或加号 */}
      <div className="absolute inset-0 flex items-center justify-center">
        {isReverseErase ? (
          // 加号
          <div className="relative">
            <div
              className="bg-white rounded-sm absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2"
              style={{
                width: `${Math.max(brushSize * 0.4, 4)}px`,
                height: `${Math.max(brushSize * 0.1, 2)}px`,
              }}
            />
            <div
              className="bg-white rounded-sm absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2"
              style={{
                width: `${Math.max(brushSize * 0.1, 2)}px`,
                height: `${Math.max(brushSize * 0.4, 4)}px`,
              }}
            />
          </div>
        ) : (
          // 减号
          <div
            className="bg-white rounded-sm"
            style={{
              width: `${Math.max(brushSize * 0.4, 4)}px`,
              height: `${Math.max(brushSize * 0.1, 2)}px`,
            }}
          />
        )}
      </div>
    </div>
  )
}
