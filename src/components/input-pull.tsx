import { useUpdateLayoutEffect } from 'ahooks'
import { Loader, Loader2, Search, Trash2, TriangleAlert, X } from 'lucide-react'
import { useCallback, useMemo, useRef } from 'react'
import { cn } from '@/lib/utils'
import { useAdvancedEditStore } from '@/stores/advanced-edit'
import MasonryGrid from './masonry-grid'
import { AutoTextarea } from './ui/auto-textarea'
import { Button } from './ui/button'
import { Popover, PopoverContent, PopoverTrigger } from './ui/popover'

type AdvancedInputPullProps = {
  height: number
  open: boolean
  onOpenChange: (open: boolean) => void
}
export default function AdvancedInputPull({
  height = 300,
  open,
  onOpenChange,
}: AdvancedInputPullProps) {
  const {
    searchQuery,
    searchLoading,
    searchResults,
    setSearchQuery,
    searchImageFn,
    searchError,
  } = useAdvancedEditStore()
  const hasResults = useMemo(() => searchResults.length > 0, [searchResults])
  const resultsLength = useMemo(() => searchResults.length, [searchResults])
  const lastRef = useRef<HTMLDivElement>(null)
  const scrollToBottom = useCallback(() => {
    lastRef.current?.scrollIntoView(false)
  }, [])
  const hasPrompt = useMemo(() => searchQuery.length > 0, [searchQuery])
  const handleTextChange = (e: React.ChangeEvent<HTMLTextAreaElement>) => {
    setSearchQuery(e.target.value)
  }
  const handleSearch = useCallback(() => {
    searchImageFn()
    scrollToBottom()
  }, [searchImageFn, scrollToBottom])
  const handleKeyDown = useCallback(
    (e: React.KeyboardEvent<HTMLTextAreaElement>) => {
      // 如果是组合键，则不处理
      if (e.metaKey || e.shiftKey || e.ctrlKey) {
        return
      }
      if (e.key === 'Enter') {
        e.preventDefault()
        handleSearch()
      }
    },
    [handleSearch]
  )
  useUpdateLayoutEffect(() => {
    if (resultsLength > 0) {
      scrollToBottom()
    }
  }, [resultsLength])

  return (
    <Popover open={open} onOpenChange={onOpenChange}>
      <PopoverTrigger className="absolute top-0 left-0" />
      <PopoverContent
        // onPointerDownOutside={e => e.preventDefault()}
        onInteractOutside={e => e.preventDefault()}
        // onEscapeKeyDown={(e) => e.preventDefault()}
        className={cn(
          'w-screen z-40 min-h-[512px] overflow-hidden p-0 relative bg-background border-[3px] rounded-b-3xl'
        )}
        style={{
          height: `calc(100vh - ${height}px - 20px)`,
        }}
        align="start"
        sideOffset={4}
      >
        <Button
          variant="outline"
          size="icon"
          className="absolute top-2 right-2 z-50"
          onClick={() => onOpenChange(false)}
        >
          <X className="size-4" />
        </Button>
        <div className="relative h-full bg-background pl-12">
          {hasResults ? (
            <div className={cn('w-full h-full overflow-y-auto relative p-4')}>
              <div className="relative pb-36">
                <div ref={lastRef} className="h-[1px] w-full opacity-0" />
                <MasonryGrid items={searchResults} />
                <p className="text-xs text-muted-foreground text-center pt-4 opacity-30">
                  到底了...
                </p>
              </div>
            </div>
          ) : (
            <div className="h-full flex flex-col justify-center items-center gap-3 px-4 text-center">
              <h2 className="text-xl font-bold">
                {searchError ? (
                  <p className="">
                    <TriangleAlert className="size-6 -mt-1 mr-2 inline-block" />
                    {searchError}
                  </p>
                ) : searchLoading ? (
                  <>
                    <Loader className="inline-block mr-2 vertical-middle size-6 -mt-1 animate-spin" />
                    正在搜索
                    {/* 如果 searchQuery 长度大于 10，则截取前 10 个字符加 ... */}
                    <span
                      className="text-foreground-primary px-1 text-lg"
                      title={searchQuery}
                    >
                      「
                      {searchQuery.length > 10
                        ? `${searchQuery.slice(0, 10)}...`
                        : searchQuery}
                      」
                    </span>
                    中
                  </>
                ) : (
                  <>
                    <Search className="inline-block mr-2 vertical-middle size-6 -mt-1" />
                    搜索参考素材
                  </>
                )}
              </h2>
              <p className="text-sm text-muted-foreground">
                请在下方输入图片描述，按回车键开始搜索图片素材
              </p>
            </div>
          )}
          {/* <SearchInput
            className="w-full absolute bottom-4 px-4 left-1/2 -translate-x-1/2 z-50"
            onSearch={scrollToBottom}
          /> */}
          <div className="w-full absolute bottom-2 px-2 left-1/2 -translate-x-1/2 z-50">
            <div className="flex flex-col w-full rounded-xl shadow-lg border bg-zinc-50 dark:bg-zinc-900">
              <div className="flex gap-2 p-2">
                <AutoTextarea
                  className="focus-visible:ring-0 border-none shadow-none resize-none min-h-[32px] py-1.5 px-2 max-h-60 overflow-y-auto"
                  placeholder="输入图片描述，按回车键开始搜索参考素材"
                  value={searchQuery}
                  maxLength={2500}
                  disabled={searchLoading}
                  onChange={handleTextChange}
                  onKeyDown={handleKeyDown}
                />
                <Button
                  variant="ghost"
                  size="icon"
                  title="清空"
                  disabled={!hasPrompt || searchLoading}
                  className="size-8 text-muted-foreground"
                  onClick={() => setSearchQuery('')}
                >
                  <Trash2 className="size-4" />
                </Button>
                <Button
                  variant="default"
                  size="icon"
                  title="搜索参考素材"
                  disabled={!hasPrompt || searchLoading}
                  className="size-8"
                  onClick={handleSearch}
                >
                  {searchLoading ? (
                    <Loader2 className="size-6 animate-spin" />
                  ) : (
                    <Search className="size-6" />
                  )}
                </Button>
              </div>
            </div>
          </div>
        </div>
      </PopoverContent>
    </Popover>
  )
}
