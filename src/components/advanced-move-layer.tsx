import { Loader2 } from 'lucide-react'
import { useCallback, useEffect, useMemo, useRef, useState } from 'react'
import {
  applyCornerResize,
  computePrimaryAxis,
  fitCenterRect,
} from '@/lib/geometry'
import { cn } from '@/lib/utils'
import { useAdvancedEditStore } from '@/stores/advanced-edit'
import type { ImageLayer } from '@/stores/edit'

type MoveLayerProps = {
  layer: ImageLayer
}

// 0: 未开始，1: 右下角，2: 左下角，3: 右上角，4: 左上角
type ResizeDirection = 0 | 1 | 2 | 3 | 4

const MoveLayer = ({ layer }: MoveLayerProps) => {
  const canvasRef = useRef<HTMLCanvasElement>(null)
  const bufferCanvasRef = useRef<HTMLCanvasElement | null>(null)
  const originalImageRef = useRef<HTMLImageElement | null>(null)
  const isDrawing = useRef(false)
  const { type } = layer
  // Add local state for tracking position, size and rotation during active operations
  const [localLayer, setLocalLayer] = useState<ImageLayer>(layer)
  // console.info('localLayer top: ', localLayer)
  // 固定当前缩放的主轴，避免在 X/Y 之间来回切换导致抖动
  const resizePrimaryAxisRef = useRef<'x' | 'y' | null>(null)
  // 记录上一次的 worktop 原始宽高比，仅在比例变化时触发自适应
  const prevWorktopAspectRef = useRef<number | null>(null)
  // 记录上一次 worktop 尺寸快照
  const prevWorktopSizeRef = useRef<{ width: number; height: number } | null>(
    null
  )
  const {
    setForeground,
    operationType,
    brushSize,
    isGenerating,
    historyVersion,
    worktop,
  } = useAdvancedEditStore()
  const worktopMemo = useMemo(() => worktop, [worktop])
  const [isDragging, setIsDragging] = useState(false)
  const [isRotating, setIsRotating] = useState(false)
  const [isResizing, setIsResizing] = useState<ResizeDirection>(0)
  const [isDrawingState, setIsDrawingState] = useState(false)
  const isDoing = useMemo(
    () => isDragging || isRotating || isResizing || isDrawingState,
    [isDragging, isRotating, isResizing, isDrawingState]
  )
  const lastMousePos = useRef({ x: 0, y: 0 })
  const lastDrawPos = useRef({
    x: null as number | null,
    y: null as number | null,
  })
  const pendingUpdates = useRef(false)
  const animationFrameId = useRef<number | null>(null)
  const currentInitIdRef = useRef<number>(0) // 用于追踪当前初始化的 ID，解决异步加载时序问题
  const prevInitKeysRef = useRef<{
    id?: number
    base64?: string
    url?: string
    width?: number
    height?: number
    // rmbgBase64 已移除，避免其变更触发不必要的重新初始化
  } | null>(null)
  const lastHistoryVersionRef = useRef<number>(0)
  const [isImageLoading, setIsImageLoading] = useState<boolean>(false)
  const initialFitAppliedRef = useRef<number | null>(null)

  // 移除缩放逻辑，尺寸按图层与画布原始像素处理

  // 创建缓冲画布
  useEffect(() => {
    if (!bufferCanvasRef.current) {
      bufferCanvasRef.current = document.createElement('canvas')
    }
    return () => {
      bufferCanvasRef.current = null
      originalImageRef.current = null
      if (animationFrameId.current) {
        cancelAnimationFrame(animationFrameId.current)
      }
    }
  }, [])

  // Update local state whenever the layer prop changes (but not during operations)
  useEffect(() => {
    if (!isDoing) {
      setLocalLayer(layer)
    }
  }, [layer, isDoing])

  // 当图层的 URL 或 ID 发生变化时，强制清除初始化缓存以确保重新初始化
  useEffect(() => {
    const currentUrl = localLayer.url
    const currentId = localLayer.id

    if (currentUrl && currentId) {
      // 如果缓存中的 URL 或 ID 与当前不同，清除缓存
      const prev = prevInitKeysRef.current
      if (prev && (prev.url !== currentUrl || prev.id !== currentId)) {
        console.info('检测到图层 URL 或 ID 变化，清除初始化缓存', {
          previousUrl: prev.url,
          currentUrl: currentUrl,
          previousId: prev.id,
          currentId: currentId,
        })
        prevInitKeysRef.current = null
      }
    }
  }, [localLayer.url, localLayer.id])

  // 预加载原始图像，避免每次绘制时都要加载
  useEffect(() => {
    // 反向涂抹需要使用原始图像，优先加载 base64（原始图像）
    if (localLayer.base64) {
      setIsImageLoading(true)
      const img = new Image()
      img.crossOrigin = 'anonymous'
      img.onload = () => {
        originalImageRef.current = img
        setIsImageLoading(false)
        console.info('原始图像加载完成')
      }
      img.onerror = () => setIsImageLoading(false)
      img.src = localLayer.base64
    } else if (localLayer.url) {
      setIsImageLoading(true)
      const img = new Image()
      img.crossOrigin = 'anonymous'
      img.onload = () => {
        originalImageRef.current = img
        setIsImageLoading(false)
        // console.info('原始图像从 URL 加载完成', img.src)
      }
      img.onerror = () => setIsImageLoading(false)
      img.src = localLayer.url
    }
  }, [localLayer.base64, localLayer.url])

  // 初始化画布 - 使用 canvas 显示图像内容（支持高分屏）
  useEffect(() => {
    const prev = prevInitKeysRef.current
    const baseImageChanged =
      !prev ||
      localLayer.id !== prev.id ||
      Number(localLayer.width) !== prev.width ||
      Number(localLayer.height) !== prev.height ||
      localLayer.base64 !== prev.base64 ||
      localLayer.url !== prev.url
    const historyChanged = historyVersion !== lastHistoryVersionRef.current

    // 添加强制重新初始化的检查：当有图像源但画布为空时
    const hasImageSource =
      localLayer.url || localLayer.base64 || localLayer.rmbgBase64
    const canvasEmpty =
      canvasRef.current &&
      canvasRef.current.width === 0 &&
      canvasRef.current.height === 0
    const forceReinit = hasImageSource && canvasEmpty

    console.info('初始化画布检查', {
      baseImageChanged,
      historyChanged,
      forceReinit,
      canvasEmpty,
      hasImageSource,
      isResizing,
      isDragging,
      isRotating,
      isDrawingState,
      layerInfo: {
        id: localLayer.id,
        url: localLayer.url,
        base64: localLayer.base64 ? 'exists' : 'none',
        rmbgBase64: localLayer.rmbgBase64 ? 'exists' : 'none',
        width: localLayer.width,
        height: localLayer.height,
      },
      previousSnapshot: prev
        ? {
            id: prev.id,
            url: prev.url,
            base64: prev.base64 ? 'exists' : 'none',
            width: prev.width,
            height: prev.height,
          }
        : null,
    })

    // 仅在"基础图像变更"、"历史变更"或"强制重新初始化"时才初始化，其他情况（移动/缩放/涂抹/反向涂抹）不初始化
    if (
      (!baseImageChanged && !historyChanged && !forceReinit) ||
      isResizing ||
      isDragging ||
      isRotating ||
      isDrawingState
    ) {
      console.info('跳过初始化画布', {
        reason: '条件不满足',
        baseImageChanged,
        historyChanged,
        forceReinit,
        isOperating: isResizing || isDragging || isRotating || isDrawingState,
      })
      return
    }

    if (canvasRef.current) {
      console.info('开始初始化画布')
      const canvas = canvasRef.current
      const buffer = bufferCanvasRef.current

      if (!buffer) {
        console.warn('缓冲画布不可用，跳过初始化')
        return
      }

      // 逻辑大小（CSS 尺寸）：基于 worktop 做等比约束，最大不超过工作区
      const width = Number(localLayer.width)
      const height = Number(localLayer.height)
      const maxW =
        worktopMemo?.width ?? prevWorktopSizeRef.current?.width ?? width
      const maxH =
        worktopMemo?.height ?? prevWorktopSizeRef.current?.height ?? height

      // 添加边界检查
      if (width <= 0 || height <= 0) {
        console.warn('图层尺寸无效', { width, height })
        return
      }
      if (maxW <= 0 || maxH <= 0) {
        console.warn('工作区尺寸无效', { maxW, maxH })
        return
      }

      const scale = Math.min(
        maxW / Math.max(1, width),
        maxH / Math.max(1, height),
        1
      )
      const drawWidth = Math.max(1, Math.floor(width * scale))
      const drawHeight = Math.max(1, Math.floor(height * scale))

      console.info('画布尺寸计算', {
        原始尺寸: { width, height },
        工作区: { maxW, maxH },
        缩放比例: scale,
        绘制尺寸: { drawWidth, drawHeight },
      })

      // 物理像素比例
      const dpr = window.devicePixelRatio || 1

      // 设置画布像素尺寸（仅在变化时设置，避免闪烁）
      const nextCanvasWidth = Math.max(1, Math.floor(drawWidth * dpr))
      const nextCanvasHeight = Math.max(1, Math.floor(drawHeight * dpr))
      if (canvas.width !== nextCanvasWidth) canvas.width = nextCanvasWidth
      if (canvas.height !== nextCanvasHeight) canvas.height = nextCanvasHeight
      if (buffer.width !== nextCanvasWidth) buffer.width = nextCanvasWidth
      if (buffer.height !== nextCanvasHeight) buffer.height = nextCanvasHeight

      // 设置画布的 CSS 尺寸，确保与逻辑尺寸一致
      canvas.style.width = `${drawWidth}px`
      canvas.style.height = `${drawHeight}px`

      console.info('画布尺寸设置', {
        cssSize: { width: drawWidth, height: drawHeight },
        pixelSize: { width: nextCanvasWidth, height: nextCanvasHeight },
        dpr,
      })

      // 提前准备好缓冲画布
      const bufferCtx = buffer.getContext('2d', { willReadFrequently: true })
      const canvasCtx = canvas.getContext('2d', { willReadFrequently: true })

      if (!bufferCtx || !canvasCtx) return

      // 缩放到逻辑坐标系（CSS 像素）
      bufferCtx.setTransform(dpr, 0, 0, dpr, 0, 0)
      canvasCtx.setTransform(dpr, 0, 0, dpr, 0, 0)

      // 提升插值质量
      bufferCtx.imageSmoothingEnabled = true
      canvasCtx.imageSmoothingEnabled = true
      // 可选：提升插值质量（有些浏览器支持）
      // @ts-ignore
      if (typeof bufferCtx.imageSmoothingQuality !== 'undefined') {
        // @ts-ignore
        bufferCtx.imageSmoothingQuality = 'high'
      }
      // @ts-ignore
      if (typeof canvasCtx.imageSmoothingQuality !== 'undefined') {
        // @ts-ignore
        canvasCtx.imageSmoothingQuality = 'high'
      }

      // 准备要绘制的图像 - 显示当前状态的图像（可能是处理过的）
      const drawImage = () => {
        // 生成当前初始化的唯一 ID，防止异步加载时序问题
        const initId = ++currentInitIdRef.current
        console.info('开始初始化', { initId })

        // 检查图像源的有效性
        const rmbgSrc = localLayer.rmbgBase64
        const baseSrc = localLayer.base64
        const urlSrc = localLayer.url

        let imageSrc = ''
        let srcType = ''

        if (rmbgSrc) {
          imageSrc = rmbgSrc
          srcType = 'rmbgBase64'
        } else if (baseSrc) {
          imageSrc = baseSrc
          srcType = 'base64'
        } else if (urlSrc) {
          imageSrc = urlSrc
          srcType = 'url'
        }

        if (!imageSrc) {
          console.error('没有可用的图像源', {
            rmbgBase64: rmbgSrc ? 'exists' : 'none',
            base64: baseSrc ? 'exists' : 'none',
            url: urlSrc || 'none',
          })
          setIsImageLoading(false)
          return
        }

        console.info('开始加载图像', {
          initId,
          srcType,
          imageSrc: `${imageSrc.substring(0, 100)}...`,
        })

        setIsImageLoading(true)
        const img = new Image()
        img.crossOrigin = 'anonymous'

        img.onload = () => {
          // 检查是否为最新的初始化请求
          if (initId !== currentInitIdRef.current) {
            console.info('忽略过期的图像加载', {
              initId,
              currentInitId: currentInitIdRef.current,
              srcType,
            })
            return
          }

          try {
            console.info('图像加载成功，开始绘制到画布', {
              initId,
              srcType,
              imageSize: { width: img.naturalWidth, height: img.naturalHeight },
              drawSize: { drawWidth, drawHeight },
              canvasSize: {
                width: canvas.width,
                height: canvas.height,
                cssWidth: canvas.style.width,
                cssHeight: canvas.style.height,
              },
              crossOrigin: img.crossOrigin,
              complete: img.complete,
              src: `${img.src.substring(0, 50)}...`,
            })

            // 检查上下文是否有效
            console.info('绘制前画布状态', {
              bufferCtxValid: !!bufferCtx,
              canvasCtxValid: !!canvasCtx,
              bufferCanvasSize: { width: buffer.width, height: buffer.height },
              canvasSize: { width: canvas.width, height: canvas.height },
              canvasDOMRect: canvas.getBoundingClientRect(),
              canvasStyles: {
                width: canvas.style.width,
                height: canvas.style.height,
                position: window.getComputedStyle(canvas).position,
                zIndex: window.getComputedStyle(canvas).zIndex,
                display: window.getComputedStyle(canvas).display,
              },
            })

            // 初始化画布，显示当前状态的图像
            console.info('开始绘制图像', {
              bufferCtxTransform: bufferCtx.getTransform(),
              canvasCtxTransform: canvasCtx.getTransform(),
              drawParams: { drawWidth, drawHeight },
              imageParams: {
                naturalWidth: img.naturalWidth,
                naturalHeight: img.naturalHeight,
              },
            })

            // 清空画布
            bufferCtx.clearRect(0, 0, buffer.width, buffer.height)
            canvasCtx.clearRect(0, 0, canvas.width, canvas.height)

            console.info('开始绘制图像到画布', {
              canvasSize: { width: canvas.width, height: canvas.height },
              imageSize: { width: img.naturalWidth, height: img.naturalHeight },
              imageComplete: img.complete,
              imageSrc: `${img.src.substring(0, 50)}...`,
            })

            // 详细检查图像状态
            console.info('图像详细状态检查', {
              src: img.src,
              crossOrigin: img.crossOrigin,
              complete: img.complete,
              naturalWidth: img.naturalWidth,
              naturalHeight: img.naturalHeight,
              width: img.width,
              height: img.height,
              currentSrc: img.currentSrc,
            })

            // 测试图像在全新 canvas 上的绘制能力
            const testCanvas = document.createElement('canvas')
            testCanvas.width = 100
            testCanvas.height = 100
            const testCtx = testCanvas.getContext('2d')

            if (testCtx) {
              try {
                testCtx.drawImage(img, 0, 0, 100, 100)
                const testResult = testCtx.getImageData(50, 50, 1, 1)
                const testPixelHasContent = Array.from(testResult.data).some(
                  pixel => pixel !== 0
                )
                console.info('全新 canvas 测试绘制', {
                  success: true,
                  hasContent: testPixelHasContent,
                  centerPixel: Array.from(testResult.data),
                })
              } catch (testError) {
                console.error('全新 canvas 测试绘制失败：', testError)
              }
            }

            // 绘制前检查画布状态
            const preDrawImageData = canvasCtx.getImageData(
              0,
              0,
              Math.min(10, canvas.width),
              Math.min(10, canvas.height)
            )
            const preDrawHasContent = Array.from(preDrawImageData.data).some(
              pixel => pixel !== 0
            )
            console.info('绘制前画布状态', {
              hasContent: preDrawHasContent,
              samplePixels: Array.from(preDrawImageData.data.slice(0, 12)),
            })

            // 直接绘制图像，使用逻辑坐标系
            try {
              console.info('准备绘制图像', {
                drawParams: {
                  x: 0,
                  y: 0,
                  width: drawWidth,
                  height: drawHeight,
                },
                canvasLogicalSize: { width: drawWidth, height: drawHeight },
                canvasPhysicalSize: {
                  width: canvas.width,
                  height: canvas.height,
                },
                currentTransform: canvasCtx.getTransform(),
                imageNaturalSize: {
                  width: img.naturalWidth,
                  height: img.naturalHeight,
                },
              })

              // 先测试绘制一个简单的矩形确保画布工作正常
              canvasCtx.save()
              canvasCtx.fillStyle = 'red'
              canvasCtx.fillRect(10, 10, 50, 50)
              canvasCtx.restore()

              // 由于变换矩阵 setTransform(dpr, 0, 0, dpr, 0, 0)
              // 逻辑坐标 (20, 20) 对应物理坐标 (20*dpr, 20*dpr)
              const currentDpr = window.devicePixelRatio || 1
              const testPhysicalX = Math.floor(20 * currentDpr)
              const testPhysicalY = Math.floor(20 * currentDpr)
              const testImageData = canvasCtx.getImageData(
                testPhysicalX,
                testPhysicalY,
                1,
                1
              )
              const testHasContent = Array.from(testImageData.data).some(
                pixel => pixel !== 0
              )
              console.info('测试矩形绘制结果', {
                hasContent: testHasContent,
                pixelData: Array.from(testImageData.data),
                logicalCoords: { x: 20, y: 20 },
                physicalCoords: { x: testPhysicalX, y: testPhysicalY },
                dpr: currentDpr,
              })

              // 如果测试矩形失败，说明变换矩阵有问题，重置并使用物理坐标
              if (!testHasContent) {
                console.warn('测试矩形绘制失败，重置变换矩阵并使用物理坐标绘制')

                // 完全重置画布状态
                console.info('完全重置 canvas 状态')

                // 1. 重置变换矩阵
                canvasCtx.resetTransform()
                bufferCtx.resetTransform()

                // 2. 重置所有 canvas 属性到默认值
                canvasCtx.globalAlpha = 1.0
                canvasCtx.globalCompositeOperation = 'source-over'
                canvasCtx.fillStyle = '#000000'
                canvasCtx.strokeStyle = '#000000'
                canvasCtx.lineWidth = 1
                canvasCtx.lineCap = 'butt'
                canvasCtx.lineJoin = 'miter'
                canvasCtx.miterLimit = 10
                canvasCtx.shadowColor = 'rgba(0, 0, 0, 0)'
                canvasCtx.shadowBlur = 0
                canvasCtx.shadowOffsetX = 0
                canvasCtx.shadowOffsetY = 0

                bufferCtx.globalAlpha = 1.0
                bufferCtx.globalCompositeOperation = 'source-over'
                bufferCtx.fillStyle = '#000000'
                bufferCtx.strokeStyle = '#000000'
                bufferCtx.lineWidth = 1
                bufferCtx.lineCap = 'butt'
                bufferCtx.lineJoin = 'miter'
                bufferCtx.miterLimit = 10
                bufferCtx.shadowColor = 'rgba(0, 0, 0, 0)'
                bufferCtx.shadowBlur = 0
                bufferCtx.shadowOffsetX = 0
                bufferCtx.shadowOffsetY = 0

                // 3. 清除画布
                canvasCtx.clearRect(0, 0, canvas.width, canvas.height)
                bufferCtx.clearRect(0, 0, buffer.width, buffer.height)

                // 4. 验证重置是否成功
                const resetTestImageData = canvasCtx.getImageData(0, 0, 10, 10)
                const isActuallyCleared = Array.from(
                  resetTestImageData.data
                ).every(pixel => pixel === 0)
                console.info('canvas 重置验证', {
                  isActuallyCleared,
                  samplePixels: Array.from(
                    resetTestImageData.data.slice(0, 12)
                  ),
                })

                // 5. 重置后再次测试绘制能力
                canvasCtx.fillStyle = 'blue'
                canvasCtx.fillRect(5, 5, 20, 20)

                const postResetTestData = canvasCtx.getImageData(10, 10, 1, 1)
                const postResetHasContent = Array.from(
                  postResetTestData.data
                ).some(pixel => pixel !== 0)
                console.info('重置后绘制测试', {
                  hasContent: postResetHasContent,
                  pixelData: Array.from(postResetTestData.data),
                })

                // 清除测试矩形
                canvasCtx.clearRect(0, 0, canvas.width, canvas.height)
                bufferCtx.clearRect(0, 0, buffer.width, buffer.height)

                // 使用物理像素尺寸绘制图像
                console.info('使用物理像素坐标绘制', {
                  physicalSize: { width: canvas.width, height: canvas.height },
                  drawSize: { width: drawWidth, height: drawHeight },
                  imageNaturalSize: {
                    width: img.naturalWidth,
                    height: img.naturalHeight,
                  },
                  drawImageParams: {
                    sx: 0,
                    sy: 0,
                    sw: img.naturalWidth,
                    sh: img.naturalHeight,
                    dx: 0,
                    dy: 0,
                    dw: canvas.width,
                    dh: canvas.height,
                  },
                })

                // 先尝试使用完整的 drawImage 参数
                try {
                  bufferCtx.drawImage(
                    img,
                    0,
                    0,
                    img.naturalWidth,
                    img.naturalHeight,
                    0,
                    0,
                    canvas.width,
                    canvas.height
                  )
                  canvasCtx.drawImage(
                    img,
                    0,
                    0,
                    img.naturalWidth,
                    img.naturalHeight,
                    0,
                    0,
                    canvas.width,
                    canvas.height
                  )
                  console.info('完整参数 drawImage 调用成功')
                } catch (fullParamError) {
                  console.warn(
                    '完整参数 drawImage 失败，尝试简化参数：',
                    fullParamError
                  )
                  bufferCtx.drawImage(img, 0, 0, canvas.width, canvas.height)
                  canvasCtx.drawImage(img, 0, 0, canvas.width, canvas.height)
                }
              } else {
                // 测试成功，清除测试矩形，继续使用逻辑坐标
                canvasCtx.clearRect(0, 0, canvas.width, canvas.height)
                bufferCtx.clearRect(0, 0, buffer.width, buffer.height)

                // 使用逻辑坐标绘制图像
                bufferCtx.drawImage(img, 0, 0, drawWidth, drawHeight)
                canvasCtx.drawImage(img, 0, 0, drawWidth, drawHeight)
              }

              console.info('图像绘制完成')

              // 立即检查绘制后的状态
              // 检查多个区域确保能找到内容
              let immediateCheckHasContent = false
              const checkAreas = [
                { x: 0, y: 0, name: '左上角' },
                {
                  x: Math.floor(canvas.width / 2),
                  y: Math.floor(canvas.height / 2),
                  name: '中央',
                },
                { x: canvas.width - 50, y: canvas.height - 50, name: '右下角' },
              ]

              for (const area of checkAreas) {
                try {
                  const areaSize = Math.min(
                    10,
                    canvas.width - area.x,
                    canvas.height - area.y
                  )
                  if (areaSize > 0) {
                    const areaImageData = canvasCtx.getImageData(
                      area.x,
                      area.y,
                      areaSize,
                      areaSize
                    )
                    const hasContent = Array.from(areaImageData.data).some(
                      pixel => pixel !== 0
                    )

                    console.info(
                      `绘制后检查区域 ${area.name} (${area.x}, ${area.y})`,
                      {
                        hasContent,
                        samplePixels: Array.from(
                          areaImageData.data.slice(0, 8)
                        ),
                      }
                    )

                    if (hasContent) {
                      immediateCheckHasContent = true
                      break
                    }
                  }
                } catch (error) {
                  console.warn(`检查区域 ${area.name} 失败:`, error)
                }
              }

              console.info('绘制后综合检查结果', {
                hasContent: immediateCheckHasContent,
              })
            } catch (error) {
              console.error('绘制失败：', error)
              throw error
            }

            // 多点验证绘制结果，考虑设备像素比
            const dpr = window.devicePixelRatio || 1
            console.info('开始验证绘制结果', {
              devicePixelRatio: dpr,
              canvasPhysicalSize: {
                width: canvas.width,
                height: canvas.height,
              },
              canvasLogicalSize: { width: drawWidth, height: drawHeight },
            })

            // 验证多个位置的像素数据
            // biome-ignore format: long explanation needed
            // 由于设置了 setTransform(dpr, 0, 0, dpr, 0, 0)，图像绘制在逻辑坐标 (0,0)-(drawWidth,drawHeight)
            // 对应的物理像素区域是 (0,0)-(drawWidth,drawHeight)，而不是整个 canvas
            const imagePhysicalWidth = drawWidth // 图像在物理像素中的实际宽度
            const imagePhysicalHeight = drawHeight // 图像在物理像素中的实际高度

            const checkPoints = [
              { x: 5, y: 5, name: '左上角' },
              {
                x: Math.floor(imagePhysicalWidth / 2),
                y: Math.floor(imagePhysicalHeight / 2),
                name: '图像中心',
              },
              {
                x: imagePhysicalWidth - 10,
                y: imagePhysicalHeight - 10,
                name: '图像右下角',
              },
            ]

            // 详细检查当前变换矩阵状态
            const currentTransformMatrix = canvasCtx.getTransform()
            // biome-ignore format: matrix comments for clarity
            console.info('当前变换矩阵详情', {
              matrix: {
                a: currentTransformMatrix.a, // x 缩放
                b: currentTransformMatrix.b, // x 倾斜  
                c: currentTransformMatrix.c, // y 倾斜
                d: currentTransformMatrix.d, // y 缩放
                e: currentTransformMatrix.e, // x 平移
                f: currentTransformMatrix.f, // y 平移
              },
              expectedDPR: dpr,
              isScaleCorrect: Math.abs(currentTransformMatrix.a - dpr) < 0.01 && Math.abs(currentTransformMatrix.d - dpr) < 0.01,
            })

            let hasAnyContent = false

            // 首先扫描整个画布查找任何非零像素
            console.info('开始全画布扫描...')
            const fullScanStep = Math.max(
              10,
              Math.floor(Math.min(canvas.width, canvas.height) / 20)
            )
            let scanFoundPixels = false

            for (let y = 0; y < canvas.height; y += fullScanStep) {
              for (let x = 0; x < canvas.width; x += fullScanStep) {
                try {
                  const imageData = canvasCtx.getImageData(x, y, 1, 1)
                  const hasPixel = Array.from(imageData.data).some(
                    pixel => pixel !== 0
                  )
                  if (hasPixel) {
                    console.info(`发现像素在 (${x}, ${y})`, {
                      pixelData: Array.from(imageData.data),
                    })
                    scanFoundPixels = true
                    hasAnyContent = true
                    break
                  }
                } catch {
                  // 忽略超出边界的错误
                }
              }
              if (scanFoundPixels) break
            }

            // 然后检查预期的关键点
            for (const point of checkPoints) {
              try {
                const sampleSize = Math.min(
                  5,
                  canvas.width - point.x,
                  canvas.height - point.y
                )
                if (sampleSize > 0) {
                  const imageData = canvasCtx.getImageData(
                    point.x,
                    point.y,
                    sampleSize,
                    sampleSize
                  )
                  const hasContent = Array.from(imageData.data).some(
                    pixel => pixel !== 0
                  )

                  console.info(
                    `验证点 ${point.name} (${point.x}, ${point.y})`,
                    {
                      hasContent,
                      sampleSize,
                      sampledPixels: Array.from(imageData.data.slice(0, 12)),
                    }
                  )

                  if (hasContent) {
                    hasAnyContent = true
                  }
                }
              } catch (error) {
                console.warn(`验证点 ${point.name} 失败:`, error)
              }
            }

            console.info('扫描结果', {
              fullScanFoundPixels: scanFoundPixels,
              pointCheckFoundPixels: hasAnyContent,
            })

            console.info('综合验证结果', {
              hasAnyContent,
              canvasSize: { width: canvas.width, height: canvas.height },
            })
            setIsImageLoading(false)
            console.info('画布绘制完成', { initId })
          } catch (error) {
            console.error('绘制图像到画布失败', error)
            setIsImageLoading(false)
          }
        }

        img.onerror = error => {
          // 检查是否为最新的初始化请求
          if (initId !== currentInitIdRef.current) {
            console.info('忽略过期的图像加载错误', {
              initId,
              currentInitId: currentInitIdRef.current,
            })
            return
          }

          console.error('图像加载失败', {
            initId,
            srcType,
            error,
            imageSrc: `${imageSrc.substring(0, 100)}...`,
          })
          setIsImageLoading(false)
        }

        img.src = imageSrc
      }

      drawImage()

      // 只有在成功初始化画布后才更新快照，避免提前更新导致的竞态问题
      // 注意：不包含 rmbgBase64，这样 rmbgBase64 的变更不会触发重新初始化
      prevInitKeysRef.current = {
        id: localLayer.id,
        base64: localLayer.base64,
        url: localLayer.url,
        width: Number(localLayer.width),
        height: Number(localLayer.height),
        // rmbgBase64 不包含在快照中，因为它可能在任务完成后被清空
        // 画布的绘制逻辑会自动处理从 rmbgBase64 到 url 的降级显示
      }

      console.info('画布初始化完成，快照已更新')

      // 标记已处理本次历史变化
      lastHistoryVersionRef.current = historyVersion
      // 记录当前 worktop 尺寸快照
      if (worktopMemo?.width && worktopMemo?.height) {
        prevWorktopSizeRef.current = {
          width: worktopMemo.width,
          height: worktopMemo.height,
        }
      }
    } else {
      console.warn('canvas ref 不可用，跳过初始化')
    }
  }, [
    localLayer.id,
    localLayer.width,
    localLayer.height,
    localLayer.base64,
    localLayer.rmbgBase64,
    localLayer.url,
    isResizing,
    isDragging,
    isRotating,
    isDrawingState,
    historyVersion,
    worktopMemo?.width,
    worktopMemo?.height,
  ])

  // 当 worktop 的 originalAspectRatio 变化时，将图层重置为居中且在 worktop 内的最大可见尺寸
  useEffect(() => {
    if (!worktopMemo || isDoing || localLayer.type !== 'image') return
    const worktopWidth = worktopMemo.width
    const worktopHeight = worktopMemo.height
    const currentAspect = worktopMemo.originalAspectRatio
    if (!worktopWidth || !worktopHeight || !currentAspect) return

    // 仅在 originalAspectRatio 发生变化时执行
    if (prevWorktopAspectRef.current === null) {
      prevWorktopAspectRef.current = currentAspect
      return
    }
    if (prevWorktopAspectRef.current === currentAspect) return

    const currentWidth = Number(localLayer.width)
    const currentHeight = Number(localLayer.height)
    if (currentWidth <= 0 || currentHeight <= 0) return

    const {
      width: nextWidth,
      height: nextHeight,
      x: nextX,
      y: nextY,
    } = fitCenterRect(
      worktopWidth,
      worktopHeight,
      currentWidth,
      currentHeight,
      { capScaleAtOne: false, minSize: 20 }
    )

    if (
      nextWidth !== currentWidth ||
      nextHeight !== currentHeight ||
      nextX !== localLayer.x ||
      nextY !== localLayer.y
    ) {
      setLocalLayer(prev => ({
        ...prev,
        width: nextWidth,
        height: nextHeight,
        x: nextX,
        y: nextY,
      }))
      try {
        console.info('更新前景宽高 1: ', nextWidth, nextHeight, nextX, nextY)
        setForeground(
          {
            ...localLayer,
            width: nextWidth,
            height: nextHeight,
            x: nextX,
            y: nextY,
          },
          { commit: false }
        )
      } catch (error) {
        console.error('Failed to update foreground on worktop resize:', error)
      }
    }

    // 更新快照
    prevWorktopAspectRef.current = currentAspect
  }, [worktopMemo, isDoing, localLayer, setForeground])

  // 初始化时：如果初始 layer 尺寸过大，则按最大可见尺寸缩放并居中
  useEffect(() => {
    if (!worktopMemo || isDoing) return
    if (!layer || layer.type !== 'image') return
    if (initialFitAppliedRef.current === layer.id) return

    const worktopWidth = worktopMemo.width
    const worktopHeight = worktopMemo.height
    if (!worktopWidth || !worktopHeight) return

    const currentWidth = Number(layer.width)
    const currentHeight = Number(layer.height)
    if (currentWidth <= 0 || currentHeight <= 0) return

    console.info(
      '初始化图层适配，图层ID:',
      layer.id,
      '尺寸:',
      currentWidth,
      'x',
      currentHeight
    )
    console.info('工作区尺寸:', worktopWidth, 'x', worktopHeight)

    const {
      width: initW,
      height: initH,
      x: initX,
      y: initY,
    } = fitCenterRect(
      worktopWidth,
      worktopHeight,
      currentWidth,
      currentHeight,
      { capScaleAtOne: true, minSize: 20 }
    )

    console.info(
      '适配后尺寸和位置:',
      initW,
      'x',
      initH,
      '位置:',
      initX,
      ',',
      initY
    )

    setLocalLayer(prev => ({
      ...prev,
      width: initW,
      height: initH,
      x: initX,
      y: initY,
    }))

    try {
      console.info('更新前景宽高 - 初始化适配：', initW, initH, initX, initY)
      setForeground(
        {
          ...layer,
          width: initW,
          height: initH,
          x: initX,
          y: initY,
        },
        { commit: true }
      )
    } catch (error) {
      console.error('Failed to update foreground on initial fit:', error)
    }

    initialFitAppliedRef.current = layer.id
  }, [layer, worktopMemo, isDoing, setForeground])

  // 当前景 URL 变化时：将图层重置为 worktop 内的最大可见尺寸并居中
  // useEffect(() => {
  //   if (!worktopMemo || isDoing || localLayer.type !== 'image') return
  //   const url = localLayer.url
  //   if (!url) return
  //   if (prevForegroundUrlRef.current === null) {
  //     prevForegroundUrlRef.current = url
  //     return
  //   }
  //   if (prevForegroundUrlRef.current === url) return

  //   const worktopWidth = worktopMemo.width
  //   const worktopHeight = worktopMemo.height
  //   if (!worktopWidth || !worktopHeight) return

  //   const currentWidth = Number(localLayer.width)
  //   const currentHeight = Number(localLayer.height)
  //   if (currentWidth <= 0 || currentHeight <= 0) return

  //   const {
  //     width: nextWidth,
  //     height: nextHeight,
  //     x: nextX,
  //     y: nextY,
  //   } = fitCenterRect(
  //     worktopWidth,
  //     worktopHeight,
  //     currentWidth,
  //     currentHeight,
  //     { capScaleAtOne: false, minSize: 20 }
  //   )

  //   setLocalLayer(prev => ({
  //     ...prev,
  //     width: nextWidth,
  //     height: nextHeight,
  //     x: nextX,
  //     y: nextY,
  //   }))
  //   try {
  //     console.info('更新前景宽高 3: ', nextWidth, nextHeight, nextX, nextY)
  //     setForeground(
  //       {
  //         ...localLayer,
  //         width: nextWidth,
  //         height: nextHeight,
  //         x: nextX,
  //         y: nextY,
  //       },
  //       { commit: false }
  //     )
  //   } catch (error) {
  //     console.error('Failed to update on foreground url change:', error)
  //   }

  //   prevForegroundUrlRef.current = url
  // }, [localLayer.url, worktopMemo, isDoing, localLayer, setForeground])

  // 延迟更新图像数据（优先使用 WebP Blob+ObjectURL，内存/体积更小）- 只在绘制结束时调用一次
  const applyCanvasChanges = useCallback(() => {
    if (
      pendingUpdates.current &&
      canvasRef.current &&
      localLayer.type === 'image'
    ) {
      try {
        const canvas = canvasRef.current
        canvas.toBlob(
          blob => {
            try {
              if (blob) {
                const objectUrl = URL.createObjectURL(blob)
                setLocalLayer(prev => ({
                  ...prev,
                  rmbgBase64: objectUrl,
                }))
                setForeground(
                  {
                    ...localLayer,
                    rmbgBase64: objectUrl,
                  },
                  { commit: true }
                )
                pendingUpdates.current = false
              } else {
                // 回退为 WebP base64
                const base64 = canvas.toDataURL('image/webp', 0.85)
                setLocalLayer(prev => ({
                  ...prev,
                  rmbgBase64: base64,
                }))
                setForeground(
                  {
                    ...localLayer,
                    rmbgBase64: base64,
                  },
                  { commit: true }
                )
                pendingUpdates.current = false
              }
            } catch (err) {
              console.error('Error handling export result:', err)
            }
          },
          'image/webp',
          0.85
        )
      } catch (error) {
        console.error('Error exporting canvas:', error)
      }
    }
  }, [localLayer, setForeground])

  // 辅助函数：在指定位置应用原始图像的圆形印章
  const stampOriginalImage = useCallback(
    (
      ctx: CanvasRenderingContext2D,
      img: HTMLImageElement,
      x: number,
      y: number,
      size: number,
      canvas: HTMLCanvasElement
    ) => {
      ctx.save()

      // 设置合成操作，确保覆盖现有内容
      ctx.globalCompositeOperation = 'source-over'

      // 创建圆形剪切区域
      ctx.beginPath()
      ctx.arc(x, y, size, 0, Math.PI * 2)
      ctx.clip()

      // 在剪切区域绘制原始图像（按逻辑尺寸绘制，避免重复缩放损失）
      try {
        const dpr = window.devicePixelRatio || 1
        const targetWidth = canvas.width / dpr
        const targetHeight = canvas.height / dpr
        ctx.drawImage(img, 0, 0, targetWidth, targetHeight)
        console.log('成功绘制原始图像印章')
      } catch (error) {
        console.error('绘制原始图像印章失败：', error)
      }

      ctx.restore()
    },
    []
  )

  // Draw on canvas
  const handleDraw = useCallback(
    (x: number, y: number) => {
      if (!canvasRef.current || !bufferCanvasRef.current) return

      const canvas = canvasRef.current
      const ctx = canvas.getContext('2d', { willReadFrequently: true })
      const bufferCanvas = bufferCanvasRef.current
      const bufferCtx = bufferCanvas.getContext('2d', {
        willReadFrequently: true,
      })

      if (!ctx || !bufferCtx) return

      // 提升插值质量（防止锯齿/粗糙）
      ctx.imageSmoothingEnabled = true
      bufferCtx.imageSmoothingEnabled = true
      // 可选：提升插值质量（有些浏览器支持）
      // @ts-ignore
      if (typeof ctx.imageSmoothingQuality !== 'undefined') {
        // @ts-ignore
        ctx.imageSmoothingQuality = 'high'
      }
      // @ts-ignore
      if (typeof bufferCtx.imageSmoothingQuality !== 'undefined') {
        // @ts-ignore
        bufferCtx.imageSmoothingQuality = 'high'
      }

      // 设置刷子属性 - 使用与画布初始化相同的缩放逻辑
      const rect = canvas.getBoundingClientRect()
      const dpr = window.devicePixelRatio || 1

      // 计算画布逻辑尺寸（CSS 像素尺寸）
      const canvasLogicalWidth = canvas.width / dpr
      const canvasLogicalHeight = canvas.height / dpr

      // 使用与画布初始化相同的缩放逻辑
      const scale = Math.min(
        canvasLogicalWidth / Math.max(1, rect.width),
        canvasLogicalHeight / Math.max(1, rect.height),
        1
      )

      const scaledBrushSize = Math.max(1, brushSize * scale)

      // 确保画布变换矩阵设置正确（处理变换矩阵被重置的情况）
      const currentTransform = ctx.getTransform()

      // 检查变换矩阵是否正确（检查缩放比例是否为 dpr）
      if (
        Math.abs(currentTransform.a - dpr) > 0.01 ||
        Math.abs(currentTransform.d - dpr) > 0.01
      ) {
        console.info('画布变换矩阵不正确，重新设置', {
          currentScaleX: currentTransform.a,
          currentScaleY: currentTransform.d,
          expectedScale: dpr,
        })
        ctx.setTransform(dpr, 0, 0, dpr, 0, 0)
        bufferCtx.setTransform(dpr, 0, 0, dpr, 0, 0)
      }
      ctx.lineWidth = scaledBrushSize * 2
      ctx.lineCap = 'round'
      ctx.lineJoin = 'round'
      bufferCtx.lineWidth = scaledBrushSize * 2
      bufferCtx.lineCap = 'round'
      bufferCtx.lineJoin = 'round'

      if (operationType === 'erase') {
        // 擦除操作
        ctx.globalCompositeOperation = 'destination-out'
        bufferCtx.globalCompositeOperation = 'destination-out'

        if (lastDrawPos.current.x === null || lastDrawPos.current.y === null) {
          // 第一个点
          ctx.beginPath()
          ctx.arc(x, y, scaledBrushSize, 0, Math.PI * 2)
          ctx.fill()

          bufferCtx.beginPath()
          bufferCtx.arc(x, y, scaledBrushSize, 0, Math.PI * 2)
          bufferCtx.fill()
        } else {
          // 连接点
          ctx.beginPath()
          ctx.moveTo(lastDrawPos.current.x, lastDrawPos.current.y)
          ctx.lineTo(x, y)
          ctx.stroke()

          bufferCtx.beginPath()
          bufferCtx.moveTo(lastDrawPos.current.x, lastDrawPos.current.y)
          bufferCtx.lineTo(x, y)
          bufferCtx.stroke()
        }
      } else if (operationType === 'reverseErase') {
        // 还原操作 - 需要从原始图像中恢复
        if (!originalImageRef.current) {
          console.warn('反向涂抹失败：原始图像未加载')
          return
        }

        console.info('执行反向涂抹，恢复原始图像')
        // 设置合成操作为 source-over，确保绘制原始图像内容
        ctx.globalCompositeOperation = 'source-over'
        bufferCtx.globalCompositeOperation = 'source-over'

        // 直接在当前画布上应用圆形印章
        stampOriginalImage(
          ctx,
          originalImageRef.current,
          x,
          y,
          scaledBrushSize,
          canvas
        )
        stampOriginalImage(
          bufferCtx,
          originalImageRef.current,
          x,
          y,
          scaledBrushSize,
          bufferCanvas
        )

        // 如果距离足够远，填充中间点
        if (lastDrawPos.current.x !== null && lastDrawPos.current.y !== null) {
          const dx = x - lastDrawPos.current.x
          const dy = y - lastDrawPos.current.y
          const distance = Math.sqrt(dx * dx + dy * dy)

          if (distance > scaledBrushSize / 2) {
            const numPoints = Math.ceil(distance / (scaledBrushSize / 3))

            for (let i = 1; i < numPoints; i++) {
              const t = i / numPoints
              const stampX = lastDrawPos.current.x + dx * t
              const stampY = lastDrawPos.current.y + dy * t

              stampOriginalImage(
                ctx,
                originalImageRef.current,
                stampX,
                stampY,
                scaledBrushSize,
                canvas
              )
              stampOriginalImage(
                bufferCtx,
                originalImageRef.current,
                stampX,
                stampY,
                scaledBrushSize,
                bufferCanvas
              )
            }
          }
        }
      }

      lastDrawPos.current = { x, y }
      pendingUpdates.current = true
    },
    [brushSize, operationType, stampOriginalImage]
  )

  const handleResizeMouseDown = (
    e: React.MouseEvent,
    direction: ResizeDirection
  ) => {
    if (operationType !== 'move') return
    setIsResizing(direction)
    lastMousePos.current = { x: e.clientX, y: e.clientY }
    resizePrimaryAxisRef.current = null
  }

  // 处理鼠标按下事件
  const handleMouseDown = (e: React.MouseEvent) => {
    if (operationType === 'move') {
      setIsDragging(true)
      lastMousePos.current = { x: e.clientX, y: e.clientY }
    } else if (
      (operationType === 'erase' || operationType === 'reverseErase') &&
      localLayer.type === 'image' &&
      canvasRef.current
    ) {
      setIsDrawingState(true)
      isDrawing.current = true
      const canvas = canvasRef.current
      const rect = canvas.getBoundingClientRect()
      const dpr = window.devicePixelRatio || 1

      // 计算画布逻辑尺寸（CSS 像素尺寸）
      const canvasLogicalWidth = canvas.width / dpr
      const canvasLogicalHeight = canvas.height / dpr

      // 使用与画布初始化相同的缩放逻辑
      const scale = Math.min(
        canvasLogicalWidth / Math.max(1, rect.width),
        canvasLogicalHeight / Math.max(1, rect.height),
        1
      )

      // 将屏幕坐标映射到画布逻辑坐标（CSS 像素）
      const x = (e.clientX - rect.left) * scale
      const y = (e.clientY - rect.top) * scale

      // 重置绘制位置
      lastDrawPos.current = { x: null, y: null }
      lastMousePos.current = { x, y }
      handleDraw(x, y)
    }
  }

  // 处理鼠标移动事件
  const handleGlobalMouseMove = useCallback(
    (e: MouseEvent) => {
      if (isDragging && operationType === 'move') {
        const deltaX = e.clientX - lastMousePos.current.x
        const deltaY = e.clientY - lastMousePos.current.y

        setLocalLayer(prev => ({
          ...prev,
          x: prev.x + deltaX,
          y: prev.y + deltaY,
        }))

        lastMousePos.current = { x: e.clientX, y: e.clientY }
      }

      if (isRotating && operationType === 'move') {
        // 计算矩形中心点
        const rect = document
          .querySelector('.current-active')
          ?.getBoundingClientRect()
        if (!rect) return

        const centerX = rect.left + rect.width / 2
        const centerY = rect.top + rect.height / 2

        // 计算鼠标相对于中心点的角度
        const mouseAngle = Math.atan2(e.clientY - centerY, e.clientX - centerX)
        const prevAngle = Math.atan2(
          lastMousePos.current.y - centerY,
          lastMousePos.current.x - centerX
        )

        // 计算角度差值并转换为度数
        const deltaAngle = (mouseAngle - prevAngle) * (180 / Math.PI)

        // 更新旋转角度，累加差值
        const newRotate = ((localLayer.rotate || 0) + deltaAngle) % 360

        setLocalLayer(prev => ({
          ...prev,
          rotate: newRotate,
        }))

        lastMousePos.current = { x: e.clientX, y: e.clientY }
      }

      // 根据不同触发点调整图片大小位置及大小
      if (
        isResizing &&
        localLayer.type === 'image' &&
        operationType === 'move'
      ) {
        if (!worktopMemo) return
        // 鼠标位移（屏幕像素）
        const deltaXScreen = e.clientX - lastMousePos.current.x
        const deltaYScreen = e.clientY - lastMousePos.current.y
        // 转换到图层自身坐标（未缩放像素）
        const deltaX = deltaXScreen
        const deltaY = deltaYScreen

        const imageLayer: ImageLayer = localLayer
        let newWidth: number = Number(imageLayer.width)
        let newHeight: number = Number(imageLayer.height)
        let newX: number = imageLayer.x
        let newY: number = imageLayer.y

        // 计算原始宽高比
        const { aspectRatio } = localLayer
        const { width: worktopWidth, height: worktopHeight } = worktopMemo

        if (!worktopWidth || !worktopHeight) return

        // 第一次移动时锁定主轴，避免临界抖动
        if (!resizePrimaryAxisRef.current) {
          resizePrimaryAxisRef.current = computePrimaryAxis(
            deltaXScreen,
            deltaYScreen
          )
        }

        // 使用通用几何工具计算新矩形
        const rect = applyCornerResize(
          isResizing as 1 | 2 | 3 | 4,
          {
            x: imageLayer.x,
            y: imageLayer.y,
            width: newWidth,
            height: newHeight,
          },
          deltaX,
          deltaY,
          aspectRatio,
          worktopWidth,
          worktopHeight,
          resizePrimaryAxisRef.current,
          { minSize: 20 }
        )
        newX = rect.x
        newY = rect.y
        newWidth = rect.width
        newHeight = rect.height

        // 确保尺寸不会太小
        if (newWidth < 20 || newHeight < 20) {
          lastMousePos.current = { x: e.clientX, y: e.clientY }
          return
        }

        setLocalLayer(prev => ({
          ...prev,
          width: newWidth,
          height: newHeight,
          x: newX,
          y: newY,
        }))

        lastMousePos.current = { x: e.clientX, y: e.clientY }
      }

      // 处理绘制
      if (
        isDrawing.current &&
        (operationType === 'erase' || operationType === 'reverseErase') &&
        canvasRef.current &&
        localLayer.type === 'image'
      ) {
        // 使用 requestAnimationFrame 优化绘制性能
        if (animationFrameId.current === null) {
          animationFrameId.current = requestAnimationFrame(() => {
            if (canvasRef.current) {
              const canvas = canvasRef.current
              const rect = canvas.getBoundingClientRect()
              const dpr = window.devicePixelRatio || 1

              // 计算画布逻辑尺寸（CSS 像素尺寸）
              const canvasLogicalWidth = canvas.width / dpr
              const canvasLogicalHeight = canvas.height / dpr

              // 使用与画布初始化相同的缩放逻辑
              const scale = Math.min(
                canvasLogicalWidth / Math.max(1, rect.width),
                canvasLogicalHeight / Math.max(1, rect.height),
                1
              )

              const x = (e.clientX - rect.left) * scale
              const y = (e.clientY - rect.top) * scale

              handleDraw(x, y)

              // 重置动画帧 ID
              animationFrameId.current = null
            }
          })
        }
      }
    },
    [
      isDragging,
      operationType,
      isRotating,
      isResizing,
      localLayer,
      handleDraw,
      worktopMemo,
    ]
  )

  // 处理鼠标松开事件
  const handleGlobalMouseUp = useCallback(() => {
    // 更新 store
    if (isDragging || isRotating || isResizing) {
      // console.info('handleGlobalMouseUp', localLayer)
      try {
        setForeground(localLayer, { commit: true })
      } catch (error) {
        console.error('Failed to update foreground:', error)
      }
    }

    // 如果是绘制操作，应用 canvas 的更改
    if (isDrawing.current) {
      applyCanvasChanges()
    }

    // 重置所有状态
    setIsDragging(false)
    setIsRotating(false)
    setIsResizing(0)
    setIsDrawingState(false)
    isDrawing.current = false
    lastDrawPos.current = { x: null, y: null }

    // 取消任何待处理的动画帧
    if (animationFrameId.current) {
      cancelAnimationFrame(animationFrameId.current)
      animationFrameId.current = null
    }
  }, [
    isDragging,
    isResizing,
    isRotating,
    localLayer,
    setForeground,
    applyCanvasChanges,
  ])

  // 在组件挂载时添加全局事件监听
  useEffect(() => {
    document.body.addEventListener('mouseup', handleGlobalMouseUp)
    document.body.addEventListener('mousemove', handleGlobalMouseMove)
    document.body.addEventListener('mouseleave', handleGlobalMouseUp)
    // 在组件卸载时移除事件监听
    return () => {
      document.body.removeEventListener('mouseup', handleGlobalMouseUp)
      document.body.removeEventListener('mousemove', handleGlobalMouseMove)
      document.body.removeEventListener('mouseleave', handleGlobalMouseUp)
    }
  }, [handleGlobalMouseUp, handleGlobalMouseMove])

  // Determine cursor type based on operationType
  const getCursorStyle = () => {
    if (type !== 'image') return 'cursor-grab active:cursor-grabbing'

    switch (operationType) {
      case 'move':
        return 'cursor-grab active:cursor-grabbing'
      case 'erase':
      case 'reverseErase':
        return 'none' // 在涂抹修改模式下隐藏默认光标，使用自定义光标
      default:
        return 'cursor-grab active:cursor-grabbing'
    }
  }

  return (
    <div
      className={cn(
        'absolute group top-0 left-0 will-change-transform current-active z-10',
        getCursorStyle()
      )}
      id="advanced-move-layer"
      style={{
        width: localLayer.width,
        height: localLayer.height,
        transform: `translate(${localLayer.x}px, ${localLayer.y}px) rotate(${localLayer.rotate}deg)`,
      }}
    >
      {!isGenerating && (
        <>
          <div className="absolute z-[999] top-0 left-0 right-0 h-[1px] bg-muted-foreground" />
          <div className="absolute z-[999] top-0 right-0 bottom-0 w-[1px] bg-muted-foreground" />
          <div className="absolute z-[999] bottom-0 left-0 right-0 h-[1px] bg-muted-foreground" />
          <div className="absolute z-[999] bottom-0 left-0 top-0 w-[1px] bg-muted-foreground" />

          {localLayer.type === 'image' && operationType === 'move' && (
            <>
              {/* 右下角 */}
              <button
                type="button"
                className="absolute z-[999] select-none bottom-0 right-0 translate-x-1/2 translate-y-1/2 size-4 bg-muted-foreground border-muted-foreground cursor-nwse-resize hidden group-[.current-active]:flex rounded items-center justify-center"
                title="改变大小"
                onMouseDown={e => handleResizeMouseDown(e, 1)}
              />
              {/* 左下角 */}
              <button
                type="button"
                className="absolute z-[999] select-none bottom-0 left-0 -translate-x-1/2 translate-y-1/2 size-4 bg-muted-foreground border-muted-foreground cursor-nesw-resize hidden group-[.current-active]:flex rounded items-center justify-center"
                title="改变大小"
                onMouseDown={e => handleResizeMouseDown(e, 2)}
              />
              {/* 右上角 */}
              <button
                type="button"
                className="absolute z-[999] select-none top-0 right-0 translate-x-1/2 -translate-y-1/2 size-4 bg-muted-foreground border-muted-foreground cursor-nesw-resize hidden group-[.current-active]:flex rounded items-center justify-center"
                title="改变大小"
                onMouseDown={e => handleResizeMouseDown(e, 3)}
              />
              {/* 左上角 */}
              <button
                type="button"
                className="absolute z-[999] select-none top-0 left-0 -translate-x-1/2 -translate-y-1/2 size-4 bg-muted-foreground border-muted-foreground cursor-nwse-resize hidden group-[.current-active]:flex rounded items-center justify-center"
                title="改变大小"
                onMouseDown={e => handleResizeMouseDown(e, 4)}
              />
            </>
          )}
        </>
      )}
      {/* 只有当操作模式为反向涂抹的时候，才会显示半透明的原始 img 图像 */}
      {operationType === 'reverseErase' && !isGenerating && (
        <img
          src={localLayer.url}
          alt="原始图像"
          className="absolute top-0 left-0 w-full h-full opacity-50 z-0 pointer-events-none"
        />
      )}
      <canvas
        ref={canvasRef}
        className="absolute top-0 left-0 z-10"
        onMouseDown={handleMouseDown}
      />
      {isImageLoading && (
        <div className="absolute inset-0 z-20 flex items-center justify-center bg-background/40">
          <div className="flex items-center gap-2 rounded-md px-3 py-2 bg-background/80 shadow-sm border">
            <Loader2 className="size-4 animate-spin" />
            <span className="text-sm text-muted-foreground">图片加载中…</span>
          </div>
        </div>
      )}
    </div>
  )
}

export default MoveLayer
