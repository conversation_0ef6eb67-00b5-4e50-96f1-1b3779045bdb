import { Plus } from 'lucide-react'
import AdvancedUploadDialog from './advanced-upload-dialog'
import { Button } from './ui/button'
import { Label } from './ui/label'

export default function AdvancedLayerList() {
  // const {
  //   foregrounds,
  //   currentForeground,
  //   setCurrentForeground,
  //   reorderForegrounds,
  // } = useAdvancedEditStore()

  // const [draggedIndex, setDraggedIndex] = useState<number | null>(null)
  // const [dragOverIndex, setDragOverIndex] = useState<number | null>(null)

  // const handleDragStart = (e: React.DragEvent, originalIndex: number) => {
  //   setDraggedIndex(originalIndex)
  //   e.dataTransfer.effectAllowed = 'move'
  //   e.dataTransfer.setData('text/html', e.currentTarget.id)
  // }

  // const handleDragOver = (e: React.DragEvent, originalIndex: number) => {
  //   e.preventDefault()
  //   e.dataTransfer.dropEffect = 'move'
  //   setDragOverIndex(originalIndex)
  // }

  // const handleDragEnd = () => {
  //   setDraggedIndex(null)
  //   setDragOverIndex(null)
  // }

  // const handleDrop = (e: React.DragEvent, toOriginalIndex: number) => {
  //   e.preventDefault()
  //   if (draggedIndex !== null && draggedIndex !== toOriginalIndex) {
  //     reorderForegrounds(draggedIndex, toOriginalIndex)
  //   }
  //   setDraggedIndex(null)
  //   setDragOverIndex(null)
  // }

  // // 反转数组以便 index 小的图层显示在上面
  // const reversedForegrounds = [...foregrounds].reverse()

  // if (foregrounds.length === 0) {
  //   return null
  // }

  return (
    <div className="flex flex-col gap-2 p-4">
      <div className="flex flex-row items-center gap-2">
        <Label>图层</Label>
        <AdvancedUploadDialog>
          <Button variant="outline" size="sm" className="ml-auto">
            <Plus />
            添加
          </Button>
        </AdvancedUploadDialog>
      </div>
      {/* <div className="flex flex-col gap-2">
        {reversedForegrounds.map((foreground, reversedIndex) => {
          const originalIndex = foregrounds.length - 1 - reversedIndex
          return (
            <div
              key={originalIndex}
              className={cn(
                'flex flex-row gap-2 rounded bg-muted/50 border hover:border-muted-foreground/50 items-center cursor-move transition-all duration-200',
                {
                  'border-red-500': currentForeground?.id === foreground.id,
                  'opacity-70': draggedIndex === originalIndex,
                  'border-blue-500 border-dashed':
                    dragOverIndex === originalIndex &&
                    draggedIndex !== originalIndex,
                }
              )}
              id={`layer-drop-${foreground.id}`}
              draggable
              onDragStart={e => handleDragStart(e, originalIndex)}
              onDragOver={e => handleDragOver(e, originalIndex)}
              onDragEnd={handleDragEnd}
              onDrop={e => handleDrop(e, originalIndex)}
            >
              <div className="size-14 text-xs relative overflow-hidden bg-white-box dark:bg-black-box bg-8px rounded shadow-md user-select-none">
                {foreground.type === 'image' ? (
                  <img
                    src={
                      foreground.rmbgBase64 ||
                      foreground.url ||
                      foreground.base64
                    }
                    alt="图片已过期"
                    className="object-contain w-full h-full"
                  />
                ) : (
                  <p className="text-muted-foreground text-xs">文本</p>
                )}
              </div>
              <p className="text-xs text-muted-foreground user-select-none">
                {foreground.type === 'image' ? '图片' : '文本'}{' '}
                {reversedIndex + 1}
              </p>
              <div className="ml-auto p-2">
                <Checkbox
                  checked={currentForeground?.id === foreground.id}
                  onCheckedChange={() => {
                    setCurrentForeground(foreground)
                  }}
                />
              </div>
            </div>
          )
        })}
      </div> */}
    </div>
  )
}
