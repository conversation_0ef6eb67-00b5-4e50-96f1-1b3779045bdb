import { Square<PERSON><PERSON> } from 'lucide-react'
import { useCallback } from 'react'
import { useNavigate } from 'react-router'
import { cn } from '@/lib/utils'
import {
  type UploadDataType,
  useAdvancedEditStore,
} from '@/stores/advanced-edit'
import { Button } from './ui/button'

type AdvancedGoButtonProps = {
  loading: boolean
  image: UploadDataType
  className?: string
}
export default function AdvancedGoButton({
  loading,
  image,
  className,
}: AdvancedGoButtonProps) {
  const navigate = useNavigate()
  const { setWorktop, setForeground } = useAdvancedEditStore()
  const handleClick = useCallback(() => {
    const { url, id, width, height } = image
    const aspectRatio = width / height

    setWorktop({
      originalWidth: width,
      originalHeight: height,
      originalAspectRatio: aspectRatio,
      width: 0,
      height: 0,
    })
    setForeground({
      id,
      type: 'image',
      aspectRatio,
      width,
      height,
      url,
      x: 0,
      y: 0,
      rotate: 0,
    })
    navigate('/advanced-edit/new?from=creation')
  }, [image, setWorktop, setForeground, navigate])

  return (
    <Button
      variant="secondary"
      size="sm"
      className={cn('', className)}
      disabled={loading}
      onClick={handleClick}
    >
      <SquarePen className="size-3" />
      高级编辑
    </Button>
  )
}
