/** biome-ignore-all lint/suspicious/noArrayIndexKey: index is intentionally used */
import { cn } from '@/lib/utils'
import { useAdvancedEditStore } from '@/stores/advanced-edit'
import { Skeleton } from './ui/skeleton'

export default function AdvancedJobStaticBox() {
  const { worktop } = useAdvancedEditStore()

  if (!worktop) return null

  return (
    <div className={cn('flex gap-1 pb-4 xl:pb-2')}>
      <div className="w-14 relative">
        <Skeleton
          className="w-14 border-none select-none text-xs relative overflow-hidden bg-white-box dark:bg-black-box bg-8px rounded shadow-md"
          style={{
            aspectRatio: worktop?.originalAspectRatio,
          }}
        ></Skeleton>
      </div>
      <div
        className={cn('flex-1 gap-0.5 grid', {
          'grid-cols-2': worktop.originalAspectRatio > 1,
          'grid-cols-4': worktop.originalAspectRatio <= 1,
        })}
      >
        {[...Array(4)].map((_, index) => (
          <div
            key={`advanced-job-static-box-${index}`}
            className={cn(
              'w-full h-full bg-zinc-200 dark:bg-zinc-800 animate-pulse flex justify-center items-center first:rounded-l last:rounded-r'
            )}
            style={{
              aspectRatio: worktop.originalAspectRatio,
            }}
          >
            <p className="text-muted-foreground text-xs text-center select-none w-full whitespace-nowrap scale-75">
              生成中
            </p>
          </div>
        ))}
      </div>
    </div>
  )
}
