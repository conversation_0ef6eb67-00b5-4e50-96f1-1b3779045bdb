import { MoveDiagonal2, RefreshCw } from 'lucide-react'
import { useCallback, useEffect, useMemo, useRef, useState } from 'react'
import { cn } from '@/lib/utils'
import { type TierType, useTiersStore } from '@/stores/tier'

type ForegroundProps = {
  tier: TierType
  index: number
  proportionScale: number
}

const Foreground = ({ tier, index, proportionScale = 1 }: ForegroundProps) => {
  const {
    type,
    // id,
    x,
    y,
    width,
    height,
    value,
    name,
    rotate,
  } = tier
  const { currentTier, setCurrentTier, updateTier } = useTiersStore()
  const isCurrent = currentTier?.id === tier.id
  const [isDragging, setIsDragging] = useState(false)
  const [isRotating, setIsRotating] = useState(false)
  const [isResizing, setIsResizing] = useState(false)
  const isDoing = useMemo(
    () => isDragging || isRotating || isResizing,
    [isDragging, isRotating, isResizing]
  )
  const lastMousePos = useRef({ x: 0, y: 0 })
  const handleResizeMouseDown = (e: React.MouseEvent) => {
    setIsResizing(true)
    lastMousePos.current = { x: e.clientX, y: e.clientY }
  }
  const handleRotateMouseDown = (e: React.MouseEvent) => {
    setIsRotating(true)
    lastMousePos.current = { x: e.clientX, y: e.clientY }
  }
  // 处理鼠标按下事件
  const handleMouseDown = (e: React.MouseEvent) => {
    // if (disable) return;
    setIsDragging(true)
    lastMousePos.current = { x: e.clientX, y: e.clientY }
  }

  // 处理鼠标移动事件
  const handleGlobalMouseMove = useCallback(
    (e: MouseEvent) => {
      if (isDragging) {
        const deltaX = e.clientX - lastMousePos.current.x
        const deltaY = e.clientY - lastMousePos.current.y
        updateTier({
          ...tier,
          x: tier.x + deltaX,
          y: tier.y + deltaY,
        })
        lastMousePos.current = { x: e.clientX, y: e.clientY }
      }

      if (isRotating) {
        // 计算矩形中心点
        const rect = document
          .querySelector('.current-active')
          ?.getBoundingClientRect()
        if (!rect) return

        const centerX = rect.left + rect.width / 2
        const centerY = rect.top + rect.height / 2

        // 计算鼠标相对于中心点的角度
        const mouseAngle = Math.atan2(e.clientY - centerY, e.clientX - centerX)
        const prevAngle = Math.atan2(
          lastMousePos.current.y - centerY,
          lastMousePos.current.x - centerX
        )

        // 计算角度差值并转换为度数
        const deltaAngle = (mouseAngle - prevAngle) * (180 / Math.PI)

        // 更新旋转角度，累加差值
        const newRotate = ((tier.rotate || 0) + deltaAngle) % 360

        updateTier({
          ...tier,
          rotate: newRotate,
        })
        lastMousePos.current = { x: e.clientX, y: e.clientY }
      }

      if (isResizing && tier.type === 'image') {
        const deltaX = e.clientX - lastMousePos.current.x
        const deltaY = e.clientY - lastMousePos.current.y
        // Use the larger delta to determine new size while maintaining ratio
        const delta = Math.abs(deltaX) > Math.abs(deltaY) ? deltaX : deltaY

        let newWidth, newHeight
        if (Math.abs(deltaX) > Math.abs(deltaY)) {
          newWidth = Number(tier.width) + delta
          newHeight = newWidth / tier.aspectRatio
        } else {
          newHeight = Number(tier.height) + delta
          newWidth = newHeight * tier.aspectRatio
        }

        updateTier({
          ...tier,
          width: newWidth,
          height: newHeight,
        })

        lastMousePos.current = { x: e.clientX, y: e.clientY }
      }
    },
    [isDragging, isResizing, isRotating, tier, updateTier]
  )

  // 处理鼠标松开事件
  const handleGlobalMouseUp = useCallback(() => {
    setIsDragging(false)
    setIsRotating(false)
    setIsResizing(false)
  }, [])

  // 在组件挂载时添加全局事件监听
  useEffect(() => {
    document.body.addEventListener('mouseup', handleGlobalMouseUp)
    document.body.addEventListener('mousemove', handleGlobalMouseMove)
    document.body.addEventListener('mouseleave', handleGlobalMouseUp)
    // 在组件卸载时移除事件监听
    return () => {
      document.body.removeEventListener('mouseup', handleGlobalMouseUp)
      document.body.removeEventListener('mousemove', handleGlobalMouseMove)
      document.body.removeEventListener('mouseleave', handleGlobalMouseUp)
    }
  }, [handleGlobalMouseUp, handleGlobalMouseMove])

  return (
    <div
      className={cn('absolute group cursor-grab active:cursor-grabbing', {
        'current-active': isCurrent,
        'z-[999]': isDoing,
      })}
      style={{
        width,
        height,
        transform: `translate(${x / proportionScale}px, ${y / proportionScale}px) rotate(${rotate}deg)`,
      }}
      onMouseDown={() => setCurrentTier(tier)}
    >
      <div className="absolute z-[999] top-0 left-0 right-0 h-[3px] bg-blue-500 hidden group-hover:block group-[.current-active]:block" />
      <div className="absolute z-[999] top-0 right-0 bottom-0 w-[3px] bg-blue-500 hidden group-hover:block group-[.current-active]:block" />
      <div className="absolute z-[999] bottom-0 left-0 right-0 h-[3px] bg-blue-500 hidden group-hover:block group-[.current-active]:block" />
      <div className="absolute z-[999] bottom-0 left-0 top-0 w-[3px] bg-blue-500 hidden group-hover:block group-[.current-active]:block" />
      <div
        className="absolute z-[999] select-none bottom-0 left-1/2 -translate-x-1/2 w-8 h-8 -mb-12 bg-muted border hover:bg-blue-500 hover:text-white hover:cursor-crosshair hidden group-[.current-active]:flex rounded-full items-center justify-center"
        onMouseDown={handleRotateMouseDown}
        title={`旋转 ${Math.round(rotate || 0)}°`}
        // onMouseMove={handleMouseMove}
      >
        <RefreshCw className="w-4 h-4" />
      </div>
      {type === 'image' && (
        <div
          className="absolute z-[999] select-none bottom-0 right-0 translate-x-1/2 translate-y-1/2 w-8 h-8 bg-muted border hover:bg-blue-500 hover:text-white cursor-nwse-resize hidden group-[.current-active]:flex rounded-full items-center justify-center"
          title="改变大小"
          onMouseDown={handleResizeMouseDown}
        >
          <MoveDiagonal2 className="w-4 h-4" />
        </div>
      )}
      {type === 'image' ? (
        <img
          src={tier.base64 || tier.url}
          alt={name}
          className="w-full h-full object-cover"
          style={{
            zIndex: index,
          }}
          onMouseDown={handleMouseDown}
          // onMouseMove={handleMouseMove}
          // onWheel={handleWheel}
          // onMouseEnter={handleMouseEnter}
          // onMouseLeave={handleMouseLeave}
          draggable="false"
          onDragStart={e => e.preventDefault()}
        />
      ) : (
        <p
          className={cn('text-center py-2 px-4', {
            'cursor-text': isCurrent,
          })}
          // onMouseDown={handleMouseDown}
          style={{
            zIndex: index,
            fontSize: `${tier.fontSize}px`,
            fontFamily: tier.fontFamily,
            fontWeight: tier.fontWeight,
            color: tier.fontColor,
            direction: tier.direction,
            writingMode: tier.writingMode,
          }}
          draggable="false"
          onDragStart={e => e.preventDefault()}
        >
          {value}
        </p>
      )}
    </div>
  )
}

export default Foreground
