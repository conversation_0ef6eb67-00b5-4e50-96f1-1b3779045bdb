import { useHover } from 'ahooks'
import { Bean, Clock10, Play, RotateCcw, Type, Video } from 'lucide-react'
import { useCallback, useEffect, useMemo, useRef } from 'react'
import Markdown from 'react-markdown'
import { NavLink } from 'react-router'
import { toast } from 'sonner'
import { cn, getJobTypeClass, PromptCommandParser } from '@/lib/utils'
import {
  DiffusionStatus,
  type JobItemType,
  JobType,
  type motionType,
  useCreationStore,
} from '@/stores/creation'
import Basemap from './basemap'
import CopyrightTag from './copyright-tag'
import CreationPrompt from './creation-prompt'
import { Badge } from './ui/badge'
import { Button } from './ui/button'

type CreationBoxProps = {
  job: JobItemType
}
export default function CreationBox({ job }: CreationBoxProps) {
  const refVideoBoxs = useRef<HTMLDivElement>(null)
  const {
    id,
    text,
    created_at,
    urls = [],
    comment = '未知原因',
    status,
    type = JobType.DEFAULT,
    is_copyright = 0,
    seed,
    basemap = [],
    audits = [],
    article = '',
  } = job
  const {
    getJobInfo,
    updateJob,
    setPrompt,
    jobReroll,
    setBasemap,
    variation,
    setGenerationType,
    autoExtendVideo,
    setVideoExtend,
    setVideoFirstFrame,
    costomInputSetting,
    generationType: generationTypeStore,
  } = useCreationStore()
  const loading = useMemo(
    () =>
      !(
        status === DiffusionStatus.COMPLETED ||
        status === DiffusionStatus.FAILED
      ),
    [status]
  )
  const { data } = useMemo(
    () => PromptCommandParser.parse(text, costomInputSetting),
    [text, costomInputSetting]
  )
  const { width: w, height: h } = useMemo(
    () => data?.aspectRatio || { width: 1, height: 1 },
    [data]
  )
  const generationType = useMemo(() => {
    return getJobTypeClass(type)
  }, [type])
  const prompt = useMemo(() => {
    return generationType === 'video'
      ? data?.prompt || ''
      : data?.prompt || text
  }, [data, text, generationType])

  const handleGetJobInfo = useCallback(async () => {
    // 如果已经有 urls 或者任务已完成/失败，跳过请求
    if (
      urls.length > 0 ||
      status === DiffusionStatus.COMPLETED ||
      status === DiffusionStatus.FAILED
    ) {
      return
    }
    const { status_code, data, message } = await getJobInfo(id)

    if (status_code === 1) {
      const {
        status: jobStatus,
        urls: jobUrls,
        seed,
        audits,
        comment,
        article,
        updated_at,
      } = data

      if (jobStatus === DiffusionStatus.PROCESSING) {
        // 只有在组件仍然挂载时才设置定时器
        setTimeout(() => {
          handleGetJobInfo()
        }, 2000)
        return
      } else if (jobStatus === DiffusionStatus.COMPLETED) {
        updateJob(id, {
          status: jobStatus,
          urls: jobUrls,
          seed,
          article,
          audits,
          updatedAt: updated_at,
        })
      } else if (jobStatus === DiffusionStatus.FAILED) {
        updateJob(id, {
          status: jobStatus,
          comment,
          audits,
          updatedAt: updated_at,
        })
      }
    } else if (status_code === 0) {
      updateJob(id, {
        status: DiffusionStatus.FAILED,
        comment: message,
        updatedAt: new Date().toISOString(),
      })
      toast.error(data.comment || `生成失败`)
    }
  }, [getJobInfo, id, updateJob, urls.length, status])
  const handleGetScrollPosition = useCallback(() => {
    const scrollTop = document.getElementById(
      'creation-scroll-container'
    )?.scrollTop
    localStorage.setItem(
      'creation-scroll-position',
      scrollTop?.toString() || '0'
    )
  }, [])
  const handleVideoExtend = useCallback(
    (jobId: string, index: number, url: string, motion: motionType) => {
      if (generationTypeStore !== 'video') {
        setGenerationType('video')
      }
      setVideoExtend({
        type: 'video_extend',
        jobId,
        videoNo: index,
        motion,
        url,
        is_copyright,
      })
      setPrompt(`${prompt} --motion ${motion}`)
      toast.success('请点击“生成视频”按钮进行视频延长')
    },
    [
      generationTypeStore,
      setVideoExtend,
      is_copyright,
      setPrompt,
      prompt,
      setGenerationType,
    ]
  )
  const handleUsePrompt = useCallback(
    (all: boolean = false) => {
      if (!data) {
        return
      }

      if (all) {
        setGenerationType(generationType)
        setBasemap(basemap)
      }
      const newCommand = PromptCommandParser.buildCommand(data, true)
      setPrompt(newCommand)
    },
    [setPrompt, data, generationType, basemap, setGenerationType, setBasemap]
  )
  const isHovering = useHover(refVideoBoxs)

  useEffect(() => {
    handleGetJobInfo()
  }, [handleGetJobInfo])

  useEffect(() => {
    const videos = refVideoBoxs.current?.querySelectorAll('video')

    if (videos) {
      videos.forEach(async video => {
        try {
          if (isHovering) {
            await video.play()
          } else {
            video.pause()
          }
        } catch (error) {
          // 忽略播放被中断的错误，这是正常的用户交互行为
          if (error instanceof Error && error.name !== 'AbortError') {
            console.warn('视频播放错误：', error)
          }
        }
      })
    }
  }, [isHovering])

  return (
    <div
      className={cn(
        'flex flex-col xl:flex-row gap-2 p-1 rounded-md bg-muted/30 mb-2 xl:mb-0 xl:bg-transparent xl:hover:bg-muted',
        {
          'animate-pulse': loading,
        }
      )}
    >
      {status === DiffusionStatus.FAILED ? (
        <div className="flex-1 flex flex-col gap-2 justify-center items-center bg-muted/70 rounded-md py-12 px-4">
          <p className="text-sm text-muted-foreground text-center">生成失败</p>
          <p className="text-xs text-muted-foreground text-center">{comment}</p>
        </div>
      ) : (
        <div
          className={cn(
            'flex-1 gap-1 xl:gap-2 grid',
            generationType === 'article'
              ? 'grid-cols-1'
              : {
                  'grid-cols-2': w > h,
                  'grid-cols-4': w <= h,
                }
          )}
          ref={refVideoBoxs}
        >
          {loading ? (
            [
              ...Array(
                type === JobType.REMOVE_BACKGROUND ||
                  type === JobType.VIDEO_UPSCALE ||
                  type === JobType.UPSCALE ||
                  type === JobType.ARTICLE_GENERATION
                  ? 1
                  : 4
              ),
            ].map((_, index) => (
              <div
                key={`${index}-${id}`}
                className={cn(
                  'w-full h-full bg-zinc-200 dark:bg-zinc-800 animate-pulse flex justify-center items-center border',
                  {
                    'rounded-l-md': index === 0 || (w > h && index % 2 === 0),
                    'rounded-r-md': (w > h && index % 2 === 1) || index === 3,
                    'h-64': type === JobType.ARTICLE_GENERATION,
                  }
                )}
                style={{
                  aspectRatio:
                    type === JobType.ARTICLE_GENERATION ? 'auto' : `${w / h}`,
                }}
              >
                <p className="text-muted-foreground text-sm select-none">
                  生成中...
                </p>
              </div>
            ))
          ) : generationType === 'article' ? (
            <NavLink
              to={`/creation/${id}?index=0`}
              // viewTransition
              prefetch="viewport"
              onClick={handleGetScrollPosition}
              className="block max-h-48 xl:max-h-64 overflow-hidden cursor-pointer p-2 bg-muted w-full rounded-md relative"
            >
              {/* {({ isTransitioning }) => (<>
                  <article
                    className="prose max-w-none prose-sm dark:prose-invert "
                    style={{
                      viewTransitionName: isTransitioning ? `creation-article-${job_id}` : '',
                    }}>
                    <Markdown>{article}</Markdown>
                  </article>
                  <div className="absolute bottom-0 left-0 right-0 h-10 bg-gradient-to-t from-muted via-muted/60 via-70% to-transparent" />
                </>)} */}
              <article className="prose max-w-none prose-sm dark:prose-invert ">
                <Markdown>{article}</Markdown>
              </article>
              <div className="absolute bottom-0 left-0 right-0 h-10 bg-gradient-to-t from-muted via-muted/60 via-70% to-transparent" />
            </NavLink>
          ) : (
            urls?.map(({ url }, index) => {
              const isBad = audits[index] && !url

              return (
                <div
                  className={cn(
                    'relative bg-[length:16px_16px] bg-white-box dark:bg-black-box overflow-hidden shadow-md hover:shadow-lg transition-shadow duration-300 group',
                    {
                      'rounded-l-md': index === 0 || (w > h && index % 2 === 0),
                      'rounded-r-md':
                        index === urls.length - 1 || (w > h && index % 2 === 1),
                    }
                  )}
                  key={`${url}-${index}-${id}`}
                  style={{
                    aspectRatio: `${w / h}`,
                  }}
                >
                  {type !== JobType.REMOVE_BACKGROUND && (
                    <CopyrightTag
                      is_copyright={is_copyright}
                      className="absolute top-1 right-1 z-10"
                      align="end"
                    />
                  )}
                  <NavLink
                    to={isBad ? '' : `/creation/${id}?index=${index}`}
                    preventScrollReset
                    className={cn('absolute top-0 left-0 size-full', {
                      'pointer-events-none': isBad,
                    })}
                    // viewTransition
                    prefetch="viewport"
                    onClick={handleGetScrollPosition}
                  >
                    {({ isTransitioning }) => (
                      <>
                        {generationType === 'image' && url && (
                          <img
                            src={`${url}?x-oss-process=image/format,webp`}
                            key={`${url}-${index}-${id}`}
                            alt={`${id}-${index}`}
                            loading="lazy"
                            className="contain-layout w-full h-full object-cover"
                            style={{
                              viewTransitionName: isTransitioning
                                ? `creation-image-${index}`
                                : '',
                            }}
                          />
                        )}
                        {generationType === 'video' && url && (
                          <>
                            <Play className="size-4 absolute top-3 left-3 z-10 text-white opacity-90 group-hover:opacity-0 transition-opacity duration-300" />
                            <video
                              src={url}
                              key={`${url}-${index}-${id}`}
                              muted
                              loop
                              preload="metadata"
                              className="contain-layout w-full h-full object-cover"
                              style={{
                                viewTransitionName: isTransitioning
                                  ? `creation-video-${index}`
                                  : '',
                              }}
                            />
                          </>
                        )}
                        {isBad && (
                          <p className="absolute w-3/5 top-1/2 left-1/2 -translate-x-1/2 -translate-y-1/2 bg-muted border p-2 text-center rounded text-sm">
                            此素材的脑洞已经突破了我的想象力结界
                          </p>
                        )}
                      </>
                    )}
                  </NavLink>
                  {!isBad && (
                    <div className="absolute bottom-0 px-1 left-1/2 -translate-x-1/2 -translate-y-1/2 z-10 flex flex-wrap justify-center items-center gap-1 w-full opacity-0 group-hover:opacity-100 transition-opacity duration-300 pointer-events-none">
                      {generationType === 'image' && (
                        <>
                          {type !== JobType.REMOVE_BACKGROUND && (
                            <div className="flex gap-1">
                              <Button
                                variant="outline"
                                title="变化"
                                size="sm"
                                className="h-7 px-2 gap-1 pointer-events-auto"
                                onClick={() => variation(id, index, 0)}
                              >
                                <span className="text-xs">V</span>细微
                              </Button>
                              <Button
                                variant="outline"
                                title="变化"
                                size="sm"
                                className="h-7 px-2 gap-1 pointer-events-auto"
                                onClick={() => variation(id, index, 1)}
                              >
                                <span className="text-xs">V</span>强烈
                              </Button>
                            </div>
                          )}
                          <Button
                            variant="outline"
                            title="生成视频"
                            size="sm"
                            className="h-7 px-2 gap-1 pointer-events-auto"
                            onClick={() =>
                              setVideoFirstFrame({
                                type: 'video_first_frame',
                                url,
                                width: w,
                                height: h,
                                is_copyright,
                                jobId: id,
                                imageNo: index,
                              })
                            }
                          >
                            <Video className="size-3" />
                            视频首帧
                          </Button>
                        </>
                      )}
                      {generationType === 'video' &&
                        type !== JobType.VIDEO_UPSCALE && (
                          <>
                            <Button
                              variant="outline"
                              title="视频延长"
                              size="sm"
                              className="h-7 px-2 gap-1 pointer-events-auto"
                              onClick={() => autoExtendVideo(id, index)}
                            >
                              自动延长
                            </Button>
                            <Button
                              variant="outline"
                              title="视频延长"
                              size="sm"
                              className="h-7 px-2 gap-1 pointer-events-auto"
                              onClick={() =>
                                handleVideoExtend(id, index, url, 'low')
                              }
                            >
                              手动延长
                            </Button>
                          </>
                        )}
                    </div>
                  )}
                </div>
              )
            })
          )}
        </div>
      )}
      <div className="w-full xl:w-96 flex flex-col justify-start items-start gap-1 xl:gap-2 relative group">
        <div
          className="sticky top-2 z-10 flex flex-col gap-1 w-full"
          style={{
            viewTransitionName: `creation-image-info-${id}`,
          }}
        >
          <CreationPrompt
            prompt={prompt}
            usePrompt={handleUsePrompt}
            type={type}
          />
          {created_at && (
            <div className="">
              <Badge
                variant="outline"
                className="text-muted-foreground font-light px-1 w-auto"
              >
                <Clock10 className="size-3 mr-1" />
                {created_at}
              </Badge>
            </div>
          )}
          <Basemap
            className="-ml-2"
            basemap={basemap}
            boxType={generationType}
          />
        </div>
        <div className="xl:absolute xl:bottom-0 xl:left-0 xl:z-20 flex gap-1 w-full py-1 xl:opacity-0 xl:group-hover:opacity-100 transition-opacity duration-300">
          <div className="flex flex-wrap gap-1 flex-1">
            {/* {type !== JobType.REMOVE_BACKGROUND &&
              status !== DiffusionStatus.FAILED &&
              generationType !== 'video' && (
                <Button
                  variant="outline"
                  size="sm"
                  disabled={loading}
                  className="h-7 px-2 gap-1 font-normal"
                  onClick={() => {
                    jobReroll(id)
                  }}
                >
                  <RotateCcw className="size-3" />
                  重新生成
                </Button>
              )} */}
            {type === JobType.DEFAULT && (
              <Button
                variant="outline"
                size="sm"
                disabled={loading}
                className="h-7 px-2 gap-1 font-normal"
                onClick={() => {
                  jobReroll(id)
                }}
              >
                <RotateCcw className="size-3" />
                重新生成
              </Button>
            )}
            <Button
              variant="outline"
              size="sm"
              className="h-7 px-2 gap-1 font-normal"
              onClick={() => {
                handleUsePrompt(true)
              }}
            >
              <Type className="size-3" />
              使用整体
            </Button>
          </div>
          {!!seed && seed !== '0' && (
            <Button
              variant="outline"
              size="sm"
              title="点击复制种子"
              className="h-7 px-2 gap-1 font-normal"
              onClick={() => {
                navigator.clipboard.writeText(seed.toString())
                toast.success('复制成功')
              }}
            >
              <Bean className="size-3" />
              {seed}
            </Button>
          )}
        </div>
      </div>
    </div>
  )
}
