import {
  CircleMinus,
  CirclePlus,
  Loader2,
  Move,
  RotateCcw,
  RotateCw,
  Sparkles,
  Undo2,
} from 'lucide-react'
import { Button } from '@/components/ui/button'
import { Label } from '@/components/ui/label'
import { Slider } from '@/components/ui/slider'
import { imageProportions } from '@/config'
import { calculateRatio, cn } from '@/lib/utils'
import { useAdvancedEditStore } from '@/stores/advanced-edit'

export default function AdvancedEditTools({
  className,
}: {
  className?: string
}) {
  const {
    activeType,
    setActiveType,
    worktop,
    setWorktop,
    foreground,
    operationType,
    setOperationType,
    brushSize,
    setBrushSize,
    undo,
    redo,
    resetToOriginal,
    history,
    isSuggesting,
    suggestPrompt,
  } = useAdvancedEditStore()

  return (
    <div
      className={cn('overflow-hidden relative w-full h-full flex-1', className)}
    >
      <div className="p-4 h-full w-full flex flex-col gap-4">
        <div className="flex gap-2">
          <Button
            variant={activeType === 'edit' ? 'default' : 'outline'}
            className="flex-1"
            size="sm"
            onClick={() => setActiveType('edit')}
          >
            编辑
          </Button>
          <Button
            variant={activeType === 'retexture' ? 'default' : 'outline'}
            className="flex-1"
            size="sm"
            onClick={() => setActiveType('retexture')}
          >
            转绘
          </Button>
        </div>
        <div className="flex-1">
          {activeType === 'edit' && (
            <div className="relative flex flex-col gap-4">
              {/* <div className="grid gap-2 mt-2">
                <AdvancedUploadDialog>
                  <Button variant="outline" className="w-full">
                    <Upload />
                    上传图片
                  </Button>
                </AdvancedUploadDialog>
              </div> */}
              {/* <Button variant="outline" className="w-full">
                  <Trash />
                  清空图层
                </Button> */}
              <div className="grid gap-2">
                <Button
                  variant={operationType === 'move' ? 'default' : 'outline'}
                  className="w-full"
                  onClick={() => setOperationType('move')}
                >
                  <Move />
                  移动缩放
                </Button>
                <Button
                  variant={operationType === 'erase' ? 'default' : 'outline'}
                  className="w-full"
                  onClick={() => setOperationType('erase')}
                >
                  <CircleMinus />
                  涂抹修改
                </Button>
                <Button
                  variant={
                    operationType === 'reverseErase' ? 'default' : 'outline'
                  }
                  className="w-full"
                  onClick={() => setOperationType('reverseErase')}
                >
                  <CirclePlus />
                  反向涂抹
                </Button>
              </div>
              {foreground && (
                <div className="hidden">
                  {' '}
                  {/* grid gap-2 */}
                  <div className="flex items-center gap-2">
                    <Button
                      variant="outline"
                      className="flex-1 select-none"
                      onClick={undo}
                      disabled={history.past.length === 0}
                      title="撤销"
                    >
                      <RotateCcw className="size-4" />
                      撤销
                    </Button>
                    <Button
                      variant="outline"
                      className="flex-1 select-none"
                      onClick={redo}
                      disabled={history.future.length === 0}
                      title="恢复"
                    >
                      <RotateCw className="size-4" />
                      恢复
                    </Button>
                  </div>
                  <Button
                    variant="outline"
                    className="w-full select-none"
                    onClick={resetToOriginal}
                    disabled={!history.origin}
                    title="重置原图"
                  >
                    <Undo2 className="size-4" />
                    重置原图
                  </Button>
                </div>
              )}
              <div className="grid gap-2">
                <Button
                  variant="outline"
                  className="w-full"
                  disabled={isSuggesting || !foreground}
                  onClick={suggestPrompt}
                >
                  {isSuggesting ? (
                    <Loader2 className="size-4 animate-spin" />
                  ) : (
                    <Sparkles className="size-4" />
                  )}
                  建议提示词
                </Button>
              </div>
              {/* <div className="grid gap-2">
                    <Button
                      variant="outline"
                      className="w-full"
                      onClick={autoRemoveBackground}
                    >
                      <Eraser />
                      去除背景
                    </Button>
                  </div> */}
              {/* <div className="grid gap-2">
                <Button variant="outline" className="w-full">
                  <PartyPopper />
                  建议提示词
                </Button>
              </div> */}
              {worktop && (
                <>
                  <div className="grid gap-3">
                    <div className="flex items-center gap-2">
                      <Label>常见图片比例</Label>
                      <p className="ml-auto text-sm">
                        {calculateRatio(
                          worktop?.originalWidth || 0,
                          worktop?.originalHeight || 0
                        )}
                      </p>
                    </div>
                    <div className="grid grid-cols-4 border rounded-lg overflow-hidden">
                      {imageProportions.map(item => (
                        <button
                          className={cn(
                            'relative cursor-pointer bg-muted text-center rounded-none p-2 border -m-[1px] hover:bg-muted/80',
                            {
                              'bg-primary hover:bg-primary/80 text-primary-foreground hover:text-primary-foreground/80':
                                item.width / item.height ===
                                worktop?.originalAspectRatio,
                            }
                          )}
                          key={item.id}
                          onClick={() => {
                            if (!worktop) return
                            const { width, height } = item
                            setWorktop({
                              originalWidth: width,
                              originalHeight: height,
                              originalAspectRatio: width / height,
                            })
                          }}
                          type="button"
                        >
                          <span className="select-none text-xs">
                            {item.name}
                          </span>
                        </button>
                      ))}
                    </div>
                  </div>
                  <div className="grid gap-3">
                    <div className="flex items-center gap-2">
                      <Label>笔刷尺寸</Label>
                      <p className="ml-auto text-sm">{brushSize}</p>
                    </div>
                    <div className="">
                      <Slider
                        defaultValue={[50]}
                        value={[brushSize]}
                        max={100}
                        min={10}
                        step={1}
                        disabled={operationType === 'move'}
                        onValueChange={value => setBrushSize(value[0])}
                      />
                    </div>
                  </div>
                  {/* <div className="grid gap-3 mt-auto">
                    <div className="flex items-center gap-2">
                      <Label>导出</Label>
                    </div>
                    <div className="flex gap-4">
                      <Button
                        variant="outline"
                        className="w-full"
                        disabled={canDownload}
                        onClick={downloadJobImage}
                      >
                        <Download />
                        下载图片
                      </Button>
                    </div>
                  </div> */}
                </>
              )}
            </div>
          )}
          {activeType === 'retexture' && (
            <div className="relative flex flex-col gap-4">
              <div className="grid gap-2 text-sm text-muted-foreground">
                <p>转绘功能支持在更改图片风格/内容的同时，尽量保持图片结构。</p>
                <p>为保证生成效果，请使用和原图结构不冲突的提示词。</p>
              </div>
            </div>
          )}
        </div>
      </div>
      {/* <AdvancedLayerList /> */}
    </div>
  )
}
