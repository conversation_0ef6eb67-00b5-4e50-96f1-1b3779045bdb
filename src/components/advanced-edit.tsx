import { useDebounceFn, useSize, useUnmount } from 'ahooks'
import { useCallback, useEffect, useMemo, useRef } from 'react'
import { useAdvancedUploadSuccess } from '@/hooks/use-advanced-upload-success'
import { useAdvancedEditStore } from '@/stores/advanced-edit'
import AdvancedEditUpload from './advanced-edit-upload'
import AdvancedMoveLayer from './advanced-move-layer'

export default function AdvancedEditView() {
  const {
    worktop,
    foreground,
    activeType,
    setWorktop,
    setForeground,
    setPrompt,
  } = useAdvancedEditStore()
  const containerRef = useRef<HTMLDivElement>(null)
  const worktopRef = useRef<HTMLDivElement>(null)
  const containerSize = useSize(containerRef)
  const handleContainerSizeChange = useCallback(() => {
    if (!worktop || !containerSize) return
    const { width, height } = containerSize
    const {
      originalWidth,
      originalHeight,
      originalAspectRatio,
      width: worktopWidth = 0,
      height: worktopHeight = 0,
    } = worktop
    const containerAspectRatio = width / height

    // 计算下一次应设置的 worktop 尺寸
    const next =
      containerAspectRatio > originalAspectRatio
        ? {
            height: Math.min(height, originalHeight),
            width: Math.min(
              Math.round(height * originalAspectRatio),
              originalWidth
            ),
          }
        : {
            width: Math.min(width, originalWidth),
            height: Math.min(
              Math.round(width / originalAspectRatio),
              originalHeight
            ),
          }
    // 仅当尺寸真正发生变化时才更新，避免死循环
    const epsilon = 0.5 // 容忍浮点误差
    const widthChanged = Math.abs(worktopWidth - next.width) > epsilon
    const heightChanged = Math.abs(worktopHeight - next.height) > epsilon
    if (!widthChanged && !heightChanged) return

    setWorktop({ ...worktop, ...next })

    // if (foreground) {
    //   const { width: foregroundWidth, height: foregroundHeight, aspectRatio: foregroundAspectRatio } = foreground
    //   setForeground({
    //     ...foreground,
    //     width: next.width,
    //     height: next.height,
    //   })
    // }
  }, [worktop, containerSize, setWorktop])
  const { run } = useDebounceFn(
    () => {
      handleContainerSizeChange()
    },
    {
      wait: 100,
      maxWait: 800,
      leading: true,
    }
  )
  const { onSuccess } = useAdvancedUploadSuccess(run)
  // biome-ignore lint/correctness/useExhaustiveDependencies: dependencies intentionally limited to avoid resize loops
  useEffect(() => {
    if (!worktop) return
    run()
  }, [
    containerSize,
    worktop?.originalAspectRatio,
    worktop?.width,
    foreground?.url,
    run,
  ])

  useUnmount(() => {
    console.info('unmount')
    setWorktop(null)
    setForeground(null)
    setPrompt('')
  })

  const outerStyle = useMemo(
    () => ({
      width: worktop?.width || 0,
      height: worktop?.height || 0,
      contain: 'layout paint size',
    }),
    [worktop]
  )

  const innerStyle = useMemo(
    () => ({
      aspectRatio: worktop?.originalAspectRatio || 1,
      contain: 'layout paint',
    }),
    [worktop]
  )

  return (
    <div className="relative h-full flex-1 min-h-96 max-w-full">
      {worktop && foreground ? (
        <div
          className="h-full flex justify-center items-center relative"
          ref={containerRef}
        >
          <div
            className="bg-white-box dark:bg-black-box bg-16px shadow-lg relative max-w-full max-h-full will-change-contents"
            style={outerStyle}
            ref={worktopRef}
          >
            <div
              className="relative overflow-hidden w-full h-full"
              id="advanced-edit"
              style={innerStyle}
            >
              {activeType === 'edit' && (
                <AdvancedMoveLayer layer={foreground} />
              )}
              {activeType === 'retexture' && (
                <img
                  src={foreground.url}
                  alt="foreground"
                  className="w-full h-full object-cover select-none"
                />
              )}
            </div>
          </div>
        </div>
      ) : (
        <AdvancedEditUpload
          className="h-full max-w-lg mx-auto flex justify-center items-center flex-col overflow-hidden gap-4"
          onSuccess={onSuccess}
        />
      )}
    </div>
  )
}
