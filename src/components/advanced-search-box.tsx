import { Blend, <PERSON>, Smile } from 'lucide-react'
import { useMemo } from 'react'
import { cn } from '@/lib/utils'
import { useAdvancedEditStore } from '@/stores/advanced-edit'
import type { VCGImageType } from '@/stores/creation'
import { useImageStore } from '@/stores/image'
import { Button } from './ui/button'

type AdvancedSearchBoxProps = {
  result: VCGImageType
}
export default function AdvancedSearchBox({ result }: AdvancedSearchBoxProps) {
  const { title, url, id, width, height } = result
  const { addImageBasemap, basemap } = useAdvancedEditStore()
  const { setImageUrl } = useImageStore()
  const isSelected = useMemo(() => {
    return basemap.some(item => item.url === url)
  }, [basemap, url])

  return (
    <div className={cn('relative rounded overflow-hidden group')}>
      {isSelected && (
        <div className="absolute top-2 right-2 w-6 h-6 bg-blue-500 rounded-full flex items-center justify-center">
          <svg
            className="w-5 h-5 text-white"
            fill="currentColor"
            viewBox="0 0 20 20"
            role="img"
            aria-label="已选"
          >
            <path
              fillRule="evenodd"
              d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z"
              clipRule="evenodd"
            />
          </svg>
        </div>
      )}
      <img
        src={`${url}?x-oss-process=image/format,webp`}
        key={`${url}-${title}`}
        alt={title}
        loading="lazy"
        className="w-full min-h-32 object-cover rounded"
      />
      <button
        onClick={() => setImageUrl(url)}
        type="button"
        className="absolute top-0 left-0 w-full h-full z-10"
      />
      <div className="absolute max-w-max px-1 bottom-9 left-1/2 -translate-x-1/2 z-10 flex gap-1 items-center flex-wrap w-full justify-center opacity-0 group-hover:opacity-90 transition-opacity duration-300 pointer-events-none">
        <Button
          variant="outline"
          size="sm"
          className="h-7 px-2 gap-1 pointer-events-auto"
          onClick={() =>
            addImageBasemap([
              {
                url,
                type: 'content',
                width,
                height,
                vcgId: id,
                is_copyright: 1,
              },
            ])
          }
        >
          <Image className="size-4" />
          参照底图
        </Button>
        <Button
          variant="outline"
          size="sm"
          className="h-7 px-2 gap-1 pointer-events-auto"
          onClick={() =>
            addImageBasemap([
              {
                url,
                type: 'sref',
                width,
                height,
                vcgId: id,
                is_copyright: 1,
              },
            ])
          }
        >
          <Blend className="size-4" />
          参照风格
        </Button>
        <Button
          variant="outline"
          size="sm"
          className="h-7 px-2 gap-1 pointer-events-auto"
          onClick={() =>
            addImageBasemap([
              {
                url,
                type: 'cref',
                width,
                height,
                vcgId: id,
                is_copyright: 1,
              },
            ])
          }
        >
          <Smile className="size-4" />
          参照角色
        </Button>
      </div>
      {title && (
        <div
          className="absolute bottom-0 left-0 right-0 p-2 py-1.5 bg-black/80 text-white opacity-0 group-hover:opacity-90 transition-opacity duration-300 rounded-b"
          title={title}
        >
          <p className="text-sm truncate">{title}</p>
        </div>
      )}
    </div>
  )
}
