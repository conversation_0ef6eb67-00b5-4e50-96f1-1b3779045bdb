import { X } from 'lucide-react'
import CreationSearch from './creation-search'
import { Button } from './ui/button'
import { Popover, PopoverContent, PopoverTrigger } from './ui/popover'

type CreationInputPullProps = {
  height: number
  open: boolean
  onOpenChange: (open: boolean) => void
}
export default function CreationInputPull({
  height = 300,
  open,
  onOpenChange,
}: CreationInputPullProps) {
  return (
    <Popover open={open} onOpenChange={onOpenChange}>
      <PopoverTrigger className="absolute top-0 left-0" />
      <PopoverContent
        // onPointerDownOutside={e => e.preventDefault()}
        onInteractOutside={e => e.preventDefault()}
        // onEscapeKeyDown={(e) => e.preventDefault()}
        className="w-screen z-40 min-h-[512px] overflow-hidden p-0 relative bg-background border-[3px] rounded-b-3xl"
        style={{
          height: `calc(100vh - ${height}px - 66px)`,
        }}
        align="start"
        sideOffset={0}
      >
        <Button
          variant="outline"
          size="icon"
          className="absolute top-2 right-2 z-50"
          onClick={() => onOpenChange(false)}
        >
          <X className="size-4" />
        </Button>
        <CreationSearch />
      </PopoverContent>
    </Popover>
  )
}
