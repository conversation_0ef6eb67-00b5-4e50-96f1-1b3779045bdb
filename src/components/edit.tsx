import { useClick<PERSON><PERSON>, useSize } from 'ahooks'
import {
  ArrowDownFromLine,
  ArrowUpFromLine,
  Bold,
  Brush,
  Italic,
  Minus,
  Palette,
  Plus,
  Text,
  Trash2,
  Type,
  Underline,
} from 'lucide-react'
import { useCallback, useEffect, useMemo, useRef, useState } from 'react'
import { ChromePicker } from 'react-color'
import type { ImageInfoType } from '@/config'
import { cn } from '@/lib/utils'
import { useCommonStore } from '@/stores/common'
import { type ImageLayer, type TextLayer, useEditStore } from '@/stores/edit'
import MoveLayer from './move-layer'
import { Button } from './ui/button'
import { Input } from './ui/input'
import { Popover, PopoverContent, PopoverTrigger } from './ui/popover'
import {
  Select,
  SelectContent,
  SelectGroup,
  SelectItem,
  SelectLabel,
  SelectTrigger,
  SelectValue,
} from './ui/select'
import { ToggleGroup, ToggleGroupItem } from './ui/toggle-group'
import Upload from './upload'

const DEFAULT_TEXT_LAYER = (): TextLayer => ({
  id: Date.now(),
  type: 'text',
  width: 'auto',
  height: 'auto',
  text: '你的文字',
  x: 0,
  y: 0,
  rotate: 0,
  fontSize: 80,
  fontColor: '#000000',
})
const FONTS = [
  {
    name: '仓耳与墨',
    fontFamily: 'CangErYuMo',
    src: '/fonts/CangErYuMo.ttf',
    format: 'truetype',
  },
  {
    name: '仓耳小丸子',
    fontFamily: 'CangErXiaoWanZi',
    src: '/fonts/CangErXiaoWanZi.ttf',
    format: 'truetype',
  },
  {
    name: '仓耳渔阳体',
    fontFamily: 'CangErYuYangTi',
    src: '/fonts/CangErYuYangTi.ttf',
    format: 'truetype',
  },
  {
    name: '仓耳舒圆体',
    fontFamily: 'CangErShuYuanTi',
    src: '/fonts/CangErShuYuanTi.ttf',
    format: 'truetype',
  },
  {
    name: '小米',
    fontFamily: 'MiSans',
    src: '/fonts/MiSans.ttf',
    format: 'truetype',
  },
  {
    name: '思源黑体',
    fontFamily: 'SourceHanSans',
    src: '/fonts/SourceHanSans.otf',
    format: 'opentype',
  },
  {
    name: '汉仪平安行繁',
    fontFamily: 'HanyiPingXingFan',
    src: '/fonts/HanyiPingXingFan.ttf',
    format: 'truetype',
  },
  {
    name: '汉仪铸字童年体',
    fontFamily: 'HanyiZhuZiTongNianTi',
    src: '/fonts/HanyiZhuZiTongNianTi.ttf',
    format: 'truetype',
  },
]
export default function Edit() {
  const {
    basic,
    // setBasic,
    layers,
    currentLayer,
    setCurrentLayer,
    addLayer,
    removeLayer,
    updateLayer,
    moveLayerUp,
    moveLayerDown,
  } = useEditStore()
  const { setLoading } = useCommonStore()
  const [basicScale, setBasicScale] = useState(1)
  // const [fontLoaded, setFontLoaded] = useState(false);
  const basicSize = useMemo(
    () => ({
      width: (basic?.width || 0) * basicScale,
      height: (basic?.height || 0) * basicScale,
    }),
    [basic, basicScale]
  )
  const containerRef = useRef<HTMLDivElement>(null)
  const toolsRef = useRef<HTMLDivElement>(null)
  const basicRef = useRef<HTMLDivElement>(null)
  const colorPickerRef = useRef<HTMLDivElement>(null)
  const containerSize = useSize(containerRef)
  // const hasLayer = useMemo(() => layers.length > 0, [layers]);
  const currentLayerData = useMemo(
    () => layers.find(l => l.id === currentLayer?.id) || null,
    [layers, currentLayer]
  )
  const handleContainerSizeChange = useCallback(() => {
    if (containerSize && basic) {
      const containerHeight = (containerSize?.height || 0) - 64
      const containerAspectRatio = containerSize?.width / containerHeight
      const basicAspectRatio = basic?.aspectRatio

      if (
        basicAspectRatio > containerAspectRatio &&
        basic?.width > containerSize?.width
      ) {
        setBasicScale(containerSize?.width / basic?.width)
      } else if (
        basicAspectRatio < containerAspectRatio &&
        basic?.height > containerHeight
      ) {
        setBasicScale(containerHeight / basic?.height)
      } else {
        setBasicScale(1)
      }
    }
  }, [basic, containerSize])
  // const handleUploadSuccess = useCallback(
  //   (info: ImageInfoType) => {
  //     setBasic({
  //       id: Date.now(),
  //       type: 'image',
  //       aspectRatio: info.width / info.height,
  //       width: info.width,
  //       height: info.height,
  //       base64: info.base64,
  //       x: 0,
  //       y: 0,
  //       rotate: 0,
  //     })
  //     handleContainerSizeChange()
  //     setLoading(false)
  //   },
  //   [setBasic, handleContainerSizeChange, setLoading]
  // )
  const handleAddImage = useCallback(
    (info: ImageInfoType) => {
      if (!basic) return
      const aspectRatio = info.width / info.height
      const proportionAspectRatio = basic.aspectRatio
      let width = info.width
      let height = info.height

      if (aspectRatio > proportionAspectRatio && info.width > basicSize.width) {
        width = basicSize.width
        height = width / aspectRatio
      } else if (
        aspectRatio < proportionAspectRatio &&
        info.height > basicSize.height
      ) {
        height = basicSize.height
        width = height * aspectRatio
      } else if (info.width > basicSize.width) {
        width = basicSize.width
        height = width / aspectRatio
      }
      const layer = {
        id: Date.now(),
        type: 'image',
        aspectRatio,
        width,
        height,
        base64: info.base64,
        x: 0,
        y: 0,
        rotate: 0,
      } as ImageLayer
      addLayer(layer)
      setCurrentLayer(layer)
      setLoading(false)
    },
    [
      addLayer,
      basic,
      basicSize.height,
      basicSize.width,
      setCurrentLayer,
      setLoading,
    ]
  )
  const handleAddText = useCallback(() => {
    const layer = DEFAULT_TEXT_LAYER()
    addLayer(layer)
    setCurrentLayer(layer)
    setLoading(false)
  }, [addLayer, setCurrentLayer, setLoading])
  const handleFontFamilyChange = useCallback(
    async (value: string) => {
      if (currentLayerData?.type === 'text') {
        updateLayer({ ...currentLayerData, fontFamily: value })
        // const checkFont = document.fonts.check(`1em ${value}`);
        // if (checkFont) {
        //   updateLayer({ ...currentLayerData, fontFamily: value });
        //   return;
        // }
        // const foundFont = FONTS.find(f => f.fontFamily === value);

        // if (foundFont) {
        //   const fontUrl = (await import(`${foundFont.src}`)).default;
        //   const newFont = new FontFace(foundFont.fontFamily, `url(${fontUrl}) format('${foundFont.format}')`);
        //   try {
        //     await newFont.load();
        //     document.fonts.add(newFont);
        //     setFontLoaded(true);
        //   } catch (error) {
        //     console.error('字体加载失败：', error);
        //   }
        //   updateLayer({ ...currentLayerData, fontFamily: foundFont.fontFamily });
        // }
      }
    },
    [currentLayerData, updateLayer]
  )
  useEffect(() => {
    handleContainerSizeChange()
  }, [containerSize, handleContainerSizeChange])
  useClickAway(() => {
    setCurrentLayer(null)
  }, [basicRef, toolsRef, colorPickerRef])

  return (
    <div className="relative h-full p-4 flex-1 min-h-96 overflow-hidden">
      {basic ? (
        <>
          <div
            className="absolute p-2 top-4 left-4 right-4 shadow-lg rounded-lg bg-muted z-10 flex justify-between items-center gap-2"
            ref={toolsRef}
          >
            <div className="flex items-center gap-2">
              {!!currentLayer && (
                <>
                  <Button
                    size="sm"
                    variant="outline"
                    onClick={() => {
                      setCurrentLayer(null)
                      removeLayer(currentLayer)
                    }}
                  >
                    <Trash2 className="size-3" /> 删除图层
                  </Button>
                  {layers.length > 1 && (
                    <>
                      <Button size="sm" variant="outline" onClick={moveLayerUp}>
                        <ArrowUpFromLine className="size-3" /> 图层上移
                      </Button>
                      <Button
                        size="sm"
                        variant="outline"
                        onClick={moveLayerDown}
                      >
                        <ArrowDownFromLine className="size-3" /> 图层下移
                      </Button>
                    </>
                  )}
                  {currentLayerData?.type === 'text' && (
                    <>
                      <Select
                        value={currentLayerData.fontFamily}
                        onValueChange={handleFontFamilyChange}
                      >
                        <SelectTrigger className="w-[180px] bg-background h-8">
                          <SelectValue placeholder="选择字体" />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectGroup>
                            <SelectLabel>字体</SelectLabel>
                            {FONTS.map(font => (
                              <SelectItem
                                key={font.fontFamily}
                                value={font.fontFamily}
                              >
                                {font.name}
                              </SelectItem>
                            ))}
                          </SelectGroup>
                        </SelectContent>
                      </Select>
                      <div className="flex items-center border rounded-md bg-background shadow-sm">
                        <Button
                          className="h-[30px]"
                          size="sm"
                          variant="ghost"
                          onClick={() => {
                            updateLayer({
                              ...currentLayerData,
                              fontSize: currentLayerData.fontSize + 1,
                            })
                          }}
                        >
                          <Plus className="size-3" />
                        </Button>
                        <Input
                          type="number"
                          min={1}
                          step={1}
                          className="w-10 h-7 border-none shadow-none text-center focus-visible:ring-0 px-1 [appearance:textfield] [&::-webkit-outer-spin-button]:appearance-none [&::-webkit-inner-spin-button]:appearance-none"
                          value={currentLayerData.fontSize}
                          onChange={e => {
                            updateLayer({
                              ...currentLayerData,
                              fontSize: parseInt(e.target.value),
                            })
                          }}
                        />
                        <Button
                          className="h-[30px]"
                          size="sm"
                          variant="ghost"
                          onClick={() => {
                            updateLayer({
                              ...currentLayerData,
                              fontSize: currentLayerData.fontSize - 1,
                            })
                          }}
                        >
                          <Minus className="size-3" />
                        </Button>
                      </div>
                      <Button
                        size="sm"
                        variant="outline"
                        className={cn({
                          'bg-primary text-primary-foreground':
                            currentLayerData.fontWeight === 'bold',
                        })}
                        onClick={() => {
                          updateLayer({
                            ...currentLayerData,
                            fontWeight: currentLayerData.fontWeight
                              ? undefined
                              : 'bold',
                          })
                        }}
                      >
                        <Bold className="size-3" />
                      </Button>
                      <Button
                        size="sm"
                        variant="outline"
                        className={cn({
                          'bg-primary text-primary-foreground':
                            currentLayerData.fontStyle === 'italic',
                        })}
                        onClick={() => {
                          updateLayer({
                            ...currentLayerData,
                            fontStyle: currentLayerData.fontStyle
                              ? undefined
                              : 'italic',
                          })
                        }}
                      >
                        <Italic className="size-3" />
                      </Button>
                      <Popover>
                        <PopoverTrigger asChild>
                          <Button size="sm" variant="outline">
                            <Palette className="size-3" />
                          </Button>
                        </PopoverTrigger>
                        <PopoverContent className="p-2" ref={colorPickerRef}>
                          <ChromePicker
                            disableAlpha
                            className="!w-full !shadow-none"
                            color={currentLayerData.fontColor}
                            onChange={color => {
                              updateLayer({
                                ...currentLayerData,
                                fontColor: color.hex,
                              })
                            }}
                          />
                        </PopoverContent>
                      </Popover>
                      {/* <Popover>
                  <PopoverTrigger asChild>
                    <Button size="sm" variant="outline">
                      <ArrowLeftRight className="size-3" />
                    </Button>
                  </PopoverTrigger>
                  <PopoverContent className="p-2" ref={colorPickerRef}>
                    <ToggleGroup type="single" value={currentLayerData.direction} onValueChange={(value) => {
                      updateLayer({ ...currentLayerData, direction: value as 'ltr' | 'rtl' });
                    }}>
                      <ToggleGroupItem value="ltr" aria-label="Toggle ltr">
                        左到右
                      </ToggleGroupItem>
                      <ToggleGroupItem value="rtl" aria-label="Toggle rtl">
                        右到左
                      </ToggleGroupItem>
                    </ToggleGroup>
                  </PopoverContent>
                </Popover> */}
                      <Popover>
                        <PopoverTrigger asChild>
                          <Button
                            size="sm"
                            variant="outline"
                            className={cn({
                              'bg-primary text-primary-foreground':
                                !!currentLayerData.textDecorationLine &&
                                currentLayerData.textDecorationLine !== 'none',
                            })}
                          >
                            <Underline className="size-3" />
                          </Button>
                        </PopoverTrigger>
                        <PopoverContent
                          className="gap-2 flex flex-col"
                          ref={colorPickerRef}
                        >
                          <ToggleGroup
                            type="single"
                            value={currentLayerData.textDecorationLine}
                            onValueChange={value => {
                              updateLayer({
                                ...currentLayerData,
                                textDecorationLine: value as
                                  | 'underline'
                                  | 'line-through'
                                  | 'overline',
                              })
                            }}
                          >
                            <ToggleGroupItem
                              value="none"
                              aria-label="Toggle none"
                            >
                              无
                            </ToggleGroupItem>
                            <ToggleGroupItem
                              value="underline"
                              aria-label="Toggle underline"
                              className="underline"
                            >
                              下划线
                            </ToggleGroupItem>
                            <ToggleGroupItem
                              value="line-through"
                              aria-label="Toggle line-through"
                              className="line-through"
                            >
                              中划线
                            </ToggleGroupItem>
                            <ToggleGroupItem
                              value="overline"
                              aria-label="Toggle overline"
                              className="overline"
                            >
                              上划线
                            </ToggleGroupItem>
                          </ToggleGroup>
                          <ToggleGroup
                            type="single"
                            value={currentLayerData.textDecorationStyle}
                            onValueChange={value => {
                              updateLayer({
                                ...currentLayerData,
                                textDecorationStyle: value as
                                  | 'solid'
                                  | 'double'
                                  | 'dotted'
                                  | 'dashed',
                              })
                            }}
                          >
                            <ToggleGroupItem
                              value="solid"
                              aria-label="Toggle solid"
                              className={cn('decoration-solid', {
                                underline:
                                  currentLayerData.textDecorationLine ===
                                  'underline',
                                'line-through':
                                  currentLayerData.textDecorationLine ===
                                  'line-through',
                                overline:
                                  currentLayerData.textDecorationLine ===
                                  'overline',
                              })}
                            >
                              实线
                            </ToggleGroupItem>
                            <ToggleGroupItem
                              value="double"
                              aria-label="Toggle double"
                              className={cn('decoration-double', {
                                underline:
                                  currentLayerData.textDecorationLine ===
                                  'underline',
                                'line-through':
                                  currentLayerData.textDecorationLine ===
                                  'line-through',
                                overline:
                                  currentLayerData.textDecorationLine ===
                                  'overline',
                              })}
                            >
                              双线
                            </ToggleGroupItem>
                            <ToggleGroupItem
                              value="dotted"
                              aria-label="Toggle dotted"
                              className={cn('decoration-dotted', {
                                underline:
                                  currentLayerData.textDecorationLine ===
                                  'underline',
                                'line-through':
                                  currentLayerData.textDecorationLine ===
                                  'line-through',
                                overline:
                                  currentLayerData.textDecorationLine ===
                                  'overline',
                              })}
                            >
                              点线
                            </ToggleGroupItem>
                            <ToggleGroupItem
                              value="dashed"
                              aria-label="Toggle dashed"
                              className={cn('decoration-dashed', {
                                underline:
                                  currentLayerData.textDecorationLine ===
                                  'underline',
                                'line-through':
                                  currentLayerData.textDecorationLine ===
                                  'line-through',
                                overline:
                                  currentLayerData.textDecorationLine ===
                                  'overline',
                              })}
                            >
                              虚线
                            </ToggleGroupItem>
                            <ToggleGroupItem
                              value="wavy"
                              aria-label="Toggle wavy"
                              className={cn('decoration-wavy', {
                                underline:
                                  currentLayerData.textDecorationLine ===
                                  'underline',
                                'line-through':
                                  currentLayerData.textDecorationLine ===
                                  'line-through',
                                overline:
                                  currentLayerData.textDecorationLine ===
                                  'overline',
                              })}
                            >
                              波浪线
                            </ToggleGroupItem>
                          </ToggleGroup>
                        </PopoverContent>
                      </Popover>
                      <Popover>
                        <PopoverTrigger asChild>
                          <Button
                            size="sm"
                            variant="outline"
                            className={cn({
                              'bg-primary text-primary-foreground':
                                !!currentLayerData.writingMode &&
                                currentLayerData.writingMode !==
                                  'horizontal-tb',
                            })}
                          >
                            <Text className="size-3" />
                          </Button>
                        </PopoverTrigger>
                        <PopoverContent className="p-2" ref={colorPickerRef}>
                          <ToggleGroup
                            type="single"
                            variant="outline"
                            value={currentLayerData.writingMode}
                            className="w-full flex flex-wrap gap-2"
                            onValueChange={value => {
                              updateLayer({
                                ...currentLayerData,
                                writingMode: value as
                                  | 'horizontal-tb'
                                  | 'vertical-rl'
                                  | 'vertical-lr'
                                  | 'sideways-rl'
                                  | 'sideways-lr',
                              })
                            }}
                          >
                            <ToggleGroupItem
                              value="horizontal-tb"
                              aria-label="Toggle horizontal-tb"
                              className="size-32 p-2 text-xs"
                              style={{ writingMode: 'horizontal-tb' }}
                            >
                              内容从左到右水平流动，下一水平行位于上一行下方。
                            </ToggleGroupItem>
                            <ToggleGroupItem
                              value="vertical-rl"
                              aria-label="Toggle vertical-rl"
                              className="size-32 p-2 text-xs"
                              style={{ writingMode: 'vertical-rl' }}
                            >
                              内容从上到下垂直流动，下一垂直行位于上一行左侧。
                            </ToggleGroupItem>
                            <ToggleGroupItem
                              value="vertical-lr"
                              aria-label="Toggle vertical-lr"
                              className="size-32 p-2 text-xs"
                              style={{ writingMode: 'vertical-lr' }}
                            >
                              内容从上到下垂直流动，下一垂直行位于上一行右侧。
                            </ToggleGroupItem>
                            <ToggleGroupItem
                              value="sideways-rl"
                              aria-label="Toggle sideways-rl"
                              className="size-32 p-2 text-xs"
                              style={{ writingMode: 'sideways-rl' }}
                            >
                              内容从下到上垂直流动，所有字形都朝向右侧。
                            </ToggleGroupItem>
                            <ToggleGroupItem
                              value="sideways-lr"
                              aria-label="Toggle sideways-lr"
                              className="size-32 p-2 text-xs"
                              style={{ writingMode: 'sideways-lr' }}
                            >
                              内容从上到下垂直流动，所有字形都朝向左侧。
                            </ToggleGroupItem>
                          </ToggleGroup>
                        </PopoverContent>
                      </Popover>
                    </>
                  )}
                </>
              )}
            </div>
            <div className="flex items-center gap-2">
              <Button onClick={handleAddText} size="sm">
                <Type /> 添加文字
              </Button>
              <Upload
                onSuccess={handleAddImage}
                size="sm"
                className="max-w-1/2 mx-auto"
                text="添加图片"
              />
            </div>
          </div>
          <div
            className="h-full pt-16 flex justify-center items-center overflow-hidden"
            ref={containerRef}
          >
            <div
              className="bg-white-box dark:bg-black-box bg-16px shadow-lg relative max-w-full max-h-full overflow-hidden"
              style={{
                width: basicSize?.width,
                height: basicSize?.height,
              }}
              ref={basicRef}
            >
              <div
                className="relative overflow-hidden"
                id="image-edit"
                style={{
                  aspectRatio: basic?.aspectRatio,
                  width: basic?.width,
                  height: basic?.height,
                  transform: `scale(${basicScale})`,
                  transformOrigin: 'top left',
                }}
              >
                <img
                  src={basic?.base64}
                  alt="basic"
                  className="w-full h-full object-contain select-none"
                  draggable={false}
                />
                {layers.map((layer, index) => (
                  <MoveLayer
                    layer={layer}
                    index={index}
                    key={index}
                    scale={basicScale}
                  />
                ))}
              </div>
            </div>
          </div>
        </>
      ) : (
        <div className="h-full flex justify-center items-center overflow-hidden">
          <div className="border w-2/3 z-10 p-4 md:p-8 shadow-lg rounded-lg bg-background flex flex-col justify-center items-center gap-4">
            <h1 className="text-2xl font-bold leading-tight tracking-tighter">
              <Brush className="inline-block mr-2 vertical-middle -mt-1" />
              素材编辑器
            </h1>
            <p className="max-w-2xl text-lg text-foreground">
              上传图片，开始编辑
            </p>
            <Upload
              // onSuccess={handleUploadSuccess}
              className="max-w-1/2 mx-auto"
              text="选择一张图片"
            />
          </div>
        </div>
      )}
    </div>
  )
}
