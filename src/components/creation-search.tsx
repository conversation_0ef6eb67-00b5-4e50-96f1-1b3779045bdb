import { useUpdateLayoutEffect } from 'ahooks'
import { Loader, Search, TriangleAlert } from 'lucide-react'
import { useEffect, useMemo, useRef } from 'react'
import { cn } from '@/lib/utils'
import { useCreationStore } from '@/stores/creation'
import MasonryGrid from './masonry-grid'
import SearchBox from './search-box'
import SearchInput from './search-input'

export default function CreationSearch() {
  // const location = useLocation();
  const { searchQuery, searchLoading, searchResults, searchError } =
    useCreationStore()
  const hasResults = useMemo(() => searchResults.length > 0, [searchResults])
  const resultsLength = useMemo(() => searchResults.length, [searchResults])
  const lastRef = useRef<HTMLDivElement>(null)
  const scrollToBottom = () => {
    lastRef.current?.scrollIntoView(false)
  }
  // 恢复滚动位置
  useEffect(() => {
    const scrollPosition = localStorage.getItem('creation-scroll-position')

    if (scrollPosition) {
      document
        .getElementById('creation-scroll-container')
        ?.scrollTo(0, parseInt(scrollPosition || '0'))
    }
  }, [])
  useUpdateLayoutEffect(() => {
    if (resultsLength > 0) {
      scrollToBottom()
    }
  }, [resultsLength])

  return (
    <div className="relative h-full bg-background pl-12">
      {hasResults ? (
        <div className={cn('w-full h-full overflow-y-auto relative p-4')}>
          <div className="relative pb-36">
            <div ref={lastRef} className="h-[1px] w-full opacity-0" />
            <MasonryGrid
              items={searchResults}
              renderItem={item => <SearchBox key={item.id} result={item} />}
            />
            <p className="text-xs text-muted-foreground text-center pt-4 opacity-30">
              到底了...
            </p>
          </div>
        </div>
      ) : (
        <div className="h-full flex flex-col justify-center items-center gap-3 px-4 text-center">
          <h2 className="text-xl font-bold">
            {searchError ? (
              <p className="">
                <TriangleAlert className="size-6 -mt-1 mr-2 inline-block" />
                {searchError}
              </p>
            ) : searchLoading ? (
              <>
                <Loader className="inline-block mr-2 vertical-middle size-6 -mt-1 animate-spin" />
                正在搜索
                {/* 如果 searchQuery 长度大于 10，则截取前 10 个字符加 ... */}
                <span
                  className="text-foreground-primary px-1 text-lg"
                  title={searchQuery}
                >
                  「
                  {searchQuery.length > 10
                    ? `${searchQuery.slice(0, 10)}...`
                    : searchQuery}
                  」
                </span>
                中
              </>
            ) : (
              <>
                <Search className="inline-block mr-2 vertical-middle size-6 -mt-1" />
                搜索参考素材
              </>
            )}
          </h2>
          <p className="text-sm text-muted-foreground">
            请在下方输入图片描述，按回车键开始搜索图片素材
          </p>
        </div>
      )}
      <SearchInput
        className="w-full absolute bottom-2 px-2 left-1/2 -translate-x-1/2 z-50"
        onSearch={scrollToBottom}
      />
    </div>
  )
}
