import { useSize } from 'ahooks'
import * as React from 'react'
import { useEffect, useRef } from 'react'
import { cn } from '@/lib/utils'

const AutoTextarea = React.forwardRef<
  HTMLTextAreaElement,
  React.ComponentProps<'textarea'>
>(({ className, value, ...props }, ref) => {
  // 创建内部 ref 用于自动调整高度
  const textareaRef = useRef<HTMLTextAreaElement | null>(null)
  const bodySize = useSize(document.body)

  // 合并外部和内部 ref
  const handleRef = (element: HTMLTextAreaElement) => {
    textareaRef.current = element
    if (typeof ref === 'function') {
      ref(element)
    } else if (ref) {
      ref.current = element
    }
  }

  // 调整高度的函数
  const adjustHeight = () => {
    const textarea = textareaRef.current
    if (!textarea) return

    // 重置高度为自动，以便准确计算内容的高度
    textarea.style.height = 'auto'
    // 设置高度为内容的实际高度
    textarea.style.height = `${textarea.scrollHeight}px`
  }

  // 组件挂载后初始化高度
  useEffect(() => {
    adjustHeight()
  }, [value, bodySize])

  // 处理内容变化的事件
  const handleInput = (e: React.FormEvent<HTMLTextAreaElement>) => {
    adjustHeight()
    // 调用原来的 onInput 处理函数（如果存在）
    props.onInput?.(e)
  }

  return (
    <textarea
      className={cn(
        'flex min-h-[38px] w-full rounded-md border border-input bg-transparent px-3 py-2 text-base shadow-sm placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring disabled:cursor-not-allowed disabled:opacity-50 md:text-sm resize-none overflow-hidden',
        className
      )}
      ref={handleRef}
      onInput={handleInput}
      value={value}
      rows={1}
      {...props}
    />
  )
})
AutoTextarea.displayName = 'AutoTextarea'

export { AutoTextarea }
