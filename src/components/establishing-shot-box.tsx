import { cn } from '@/lib/utils'
import type { EstablishingShotType } from '@/stores/establishingShot'

type CreationEstablishingShotProps = {
  className?: string
  onUploadSuccess?: (establishingShot: EstablishingShotType) => void
  onClick?: (establishingShot: EstablishingShotType) => void
}
export const EstablishingShotBox = ({
  className,
  // onUploadSuccess,
  // onClick,
}: CreationEstablishingShotProps) => {
  // const { addEstablishingShots, removeEstablishingShots, establishingShots } =
  //   useEstablishingShotStore()
  // const { setLoading } = useCommonStore()
  // const handleUploadSuccess = (url: string) => {
  //   const newEstablishingShot: EstablishingShotType = {
  //     id: new Date().toISOString(),
  //     name: '',
  //     url,
  //   }
  //   addEstablishingShots(newEstablishingShot)
  //   onUploadSuccess?.(newEstablishingShot)
  //   setLoading(false)
  // }

  return (
    <div className={cn('w-full flex gap-2', className)}>
      <div className="">
        {/* <UploadReal onSuccess={handleUploadSuccess} text="上传图片" /> */}
      </div>
      <div className="flex flex-wrap gap-2">
        {/* {establishingShots.map(establishingShot => (
          <div
            key={establishingShot.id}
            onClick={() => {
              onClick?.(establishingShot)
            }}
            className="cursor-pointer relative overflow-hidden rounded-md shadow-sm border border-transparent hover:border-muted-foreground/50 transition-all duration-300"
          >
            <Button
              variant="ghost"
              size="icon"
              className="absolute z-20 top-0 right-0 size-6 p-1 text-muted-foreground rounded-none rounded-bl-lg"
              onClick={e => {
                e.stopPropagation()
                removeEstablishingShots(establishingShot.id)
              }}
            >
              <X className="size-4" />
            </Button>
            <img
              src={establishingShot.url}
              className="w-16 h-16 rounded-md object-cover"
              alt={establishingShot.name}
            />
          </div>
        ))} */}
      </div>
    </div>
  )
}
