import { LogOut, PaintbrushVertical, SquarePen, User } from 'lucide-react'
import { NavLink, Outlet, useNavigate } from 'react-router'
import ImageDialog from '@/components/image-dialog'
import { ModeToggle } from '@/components/mode-toggle'
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar'
import { Button } from '@/components/ui/button'
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuGroup,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu'
import { Toaster } from '@/components/ui/sonner'
import {
  Tooltip,
  TooltipContent,
  TooltipTrigger,
} from '@/components/ui/tooltip'
import { useCommonStore } from '@/stores/common'

export function HydrateFallback() {
  return <div>Loading...</div>
}

export default function Root() {
  const { user, logout } = useCommonStore()
  const navigate = useNavigate()

  return (
    <>
      <div className="grid h-screen w-full overflow-hidden overscroll-none pl-14">
        <aside className="fixed left-0 pl-1 top-1/2 -translate-y-1/2 z-[9999]">
          <nav className="grid gap-1 p-1 rounded-xl border shadow-lg bg-background">
            <Tooltip>
              <TooltipTrigger asChild>
                <NavLink to="/" viewTransition>
                  <Button size="icon" variant="outline" className="rounded-lg">
                    <PaintbrushVertical className="size-5" />
                    <span className="sr-only">开始想象</span>
                  </Button>
                </NavLink>
              </TooltipTrigger>
              <TooltipContent side="right" sideOffset={12} className="z-50">
                开始想象
              </TooltipContent>
            </Tooltip>
            <Tooltip>
              <TooltipTrigger asChild>
                <NavLink to="/advanced-edit" viewTransition>
                  <Button size="icon" variant="outline" className="rounded-lg">
                    <SquarePen className="size-5" />
                    <span className="sr-only">高级编辑</span>
                  </Button>
                </NavLink>
              </TooltipTrigger>
              <TooltipContent side="right" sideOffset={12} className="z-50">
                高级编辑
              </TooltipContent>
            </Tooltip>
            <Tooltip>
              <TooltipTrigger asChild>
                <ModeToggle />
              </TooltipTrigger>
              <TooltipContent side="right" sideOffset={12} className="z-50">
                切换主题
              </TooltipContent>
            </Tooltip>
            {user && (
              <DropdownMenu>
                <DropdownMenuTrigger asChild>
                  <Avatar className="size-9 rounded-lg cursor-pointer">
                    <AvatarImage src={undefined} alt={user.name} />
                    <AvatarFallback className="rounded-lg">
                      {user.name.slice(0, 2).toUpperCase()}
                    </AvatarFallback>
                  </Avatar>
                </DropdownMenuTrigger>
                <DropdownMenuContent
                  className="w-(--radix-dropdown-menu-trigger-width) min-w-56 rounded-lg"
                  side="right"
                  align="end"
                  sideOffset={12}
                >
                  <DropdownMenuLabel className="p-0 font-normal">
                    <div className="flex items-center gap-2 px-1 py-1.5 text-left text-sm">
                      <Avatar className="size-8 rounded-lg">
                        <AvatarImage src={undefined} alt={user.name} />
                        <AvatarFallback className="rounded-lg">
                          {user.name.slice(0, 2).toUpperCase()}
                        </AvatarFallback>
                      </Avatar>
                      <div className="grid flex-1 text-left text-sm leading-tight">
                        <span className="truncate font-medium">
                          {user.name}
                        </span>
                        <span className="text-muted-foreground truncate text-xs">
                          {user.email}
                        </span>
                      </div>
                    </div>
                  </DropdownMenuLabel>
                  <DropdownMenuSeparator />
                  <DropdownMenuGroup>
                    <DropdownMenuItem
                      className="cursor-pointer"
                      onClick={() => navigate('/account')}
                    >
                      <User className="size-4" />
                      <span>账号设置</span>
                    </DropdownMenuItem>
                    {/* <DropdownMenuItem>
                      <span>Billing</span>
                    </DropdownMenuItem>
                    <DropdownMenuItem>
                      <span>Notifications</span>
                    </DropdownMenuItem> */}
                  </DropdownMenuGroup>
                  <DropdownMenuSeparator />
                  <DropdownMenuItem onClick={logout} className="cursor-pointer">
                    <LogOut className="size-4" />
                    <span>退出账号</span>
                  </DropdownMenuItem>
                </DropdownMenuContent>
              </DropdownMenu>
            )}
          </nav>
        </aside>
        <Outlet />
      </div>
      <div className="h-0">
        <Toaster position="top-center" />
      </div>
      <ImageDialog />
    </>
  )
}
