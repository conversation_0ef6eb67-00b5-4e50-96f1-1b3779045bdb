import { useLoaderData, useSearchParams } from 'react-router'
import CreationEdit from '@/components/creation-edit'
import type creationDetailLoader from '@/loader/creation_detail'

export function HydrateFallback() {
  return <div>Loading...</div>
}

export default function CreationDetail() {
  const data = useLoaderData<typeof creationDetailLoader>()
  const [searchParams] = useSearchParams()
  const index = searchParams.get('index') || 0

  return (
    <div className="flex h-full overflow-y-auto flex-col overscroll-none bg-muted/40">
      <CreationEdit job={data} index={Number(index)} />
    </div>
  )
}
