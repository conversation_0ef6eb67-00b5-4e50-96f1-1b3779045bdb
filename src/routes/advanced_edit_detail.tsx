import { Plus, Undo2 } from 'lucide-react'
import { useCallback, useEffect, useState } from 'react'
import { NavLink, useLocation, useParams, useSearchParams } from 'react-router'
import { toast } from 'sonner'
import AdvancedEditView from '@/components/advanced-edit'
import AdvancedEditInput from '@/components/advanced-edit-input'
import AdvancedEditTools from '@/components/advanced-edit-tools'
import AdvancedJobBox from '@/components/advanced-job-box'
import AdvancedJobStaticBox from '@/components/advanced-job-static-box'
import CustomCursor from '@/components/custom-cursor'
import { Button } from '@/components/ui/button'
import { useAdvancedEditStore } from '@/stores/advanced-edit'

export function HydrateFallback() {
  return <div>Loading...</div>
}

export default function AdvancedEditDetail() {
  const location = useLocation()
  const { sessionid } = useParams()
  const [searchParams] = useSearchParams()
  const {
    foreground,
    jobList,
    operationType,
    brushSize,
    setBrushSize,
    resetCanvas,
    getSessionData,
    isGenerating,
    clearJobList,
  } = useAdvancedEditStore()

  const [cursorPosition, setCursorPosition] = useState({ x: 0, y: 0 })
  const [showCustomCursor, setShowCustomCursor] = useState(false)

  // 处理鼠标移动事件
  const handleMouseMove = (e: React.MouseEvent) => {
    setCursorPosition({ x: e.clientX, y: e.clientY })

    // 只有在涂抹修改或反向涂抹模式下才显示自定义光标
    const shouldShowCursor =
      operationType === 'erase' || operationType === 'reverseErase'
    setShowCustomCursor(shouldShowCursor)
  }

  // 处理滚轮事件调整笔刷尺寸
  const handleWheel = (e: React.WheelEvent) => {
    if (
      (operationType === 'erase' || operationType === 'reverseErase') &&
      !e.defaultPrevented
    ) {
      e.preventDefault()

      // 根据滚轮方向调整笔刷尺寸
      const delta = e.deltaY > 0 ? -2 : 2
      const newSize = Math.max(10, Math.min(100, brushSize + delta))
      setBrushSize(newSize)
    }
  }

  // 处理鼠标离开区域
  const handleMouseLeave = () => {
    setShowCustomCursor(false)
  }

  // biome-ignore lint/correctness/useExhaustiveDependencies: useCallback
  const handleGoToSession = useCallback(async () => {
    const { pathname } = location
    console.info('pathname: ', pathname)

    if (pathname === '/advanced-edit/new') {
      const from = searchParams.get('from')

      if (from !== 'creation') {
        resetCanvas()
      }
      clearJobList()
    } else if (
      pathname !== '/advanced-edit/new' &&
      pathname.startsWith('/advanced-edit/') &&
      pathname.length > 15 &&
      sessionid
    ) {
      const no = searchParams.get('index')
      const jobId = searchParams.get('jobId')
      getSessionData(sessionid, jobId ?? undefined, no ? Number(no) : undefined)
    } else {
      toast.error('请先选择一个编辑会话')
    }
  }, [location, sessionid, getSessionData, resetCanvas, searchParams])

  useEffect(() => {
    handleGoToSession()
  }, [handleGoToSession])

  return (
    <div className="flex flex-row h-screen overflow-hidden">
      <div className="overflow-hidden relative w-60 bg-muted/50 flex-col gap-4 hidden xl:flex">
        <AdvancedEditTools />
      </div>
      {/* biome-ignore lint/a11y/noStaticElementInteractions: This div handles mouse events for custom cursor functionality */}
      <div
        className={`flex-1 relative p-4 h-full flex flex-col gap-4 overflow-hidden ${
          operationType === 'erase' || operationType === 'reverseErase'
            ? 'cursor-none'
            : ''
        }`}
        onMouseMove={handleMouseMove}
        onMouseLeave={handleMouseLeave}
        onWheel={
          operationType === 'erase' || operationType === 'reverseErase'
            ? handleWheel
            : undefined
        }
      >
        <div className="flex-1 h-full relative will-change-contents overflow-hidden">
          <AdvancedEditView />

          {/* 自定义光标 */}
          {foreground && (
            <CustomCursor
              visible={showCustomCursor}
              brushSize={brushSize}
              x={cursorPosition.x}
              y={cursorPosition.y}
              operationType={operationType}
            />
          )}
        </div>
        <AdvancedEditInput className="z-40" />
      </div>
      <div className="overflow-hidden relative h-full w-60 bg-muted/50 flex flex-col gap-4">
        <AdvancedEditTools className="flex-1 flex xl:hidden" />
        <div className="flex-1 overflow-hidden will-change-contents overflow-y-auto border-t border-muted-foreground/20 xl:border-none">
          <div className="flex flex-col gap-3 p-4 flex-1">
            <div className="flex items-center justify-between gap-2 sticky top-4 left-4 w-full z-20">
              <NavLink to="/advanced-edit" viewTransition>
                <Button variant="outline" size="sm">
                  <Undo2 /> 返回记录
                </Button>
              </NavLink>
              <NavLink to="/advanced-edit/new" viewTransition>
                <Button variant="outline" size="sm">
                  <Plus /> 新建编辑
                </Button>
              </NavLink>
            </div>
            <div className="relative">
              {isGenerating && <AdvancedJobStaticBox />}
              {jobList.map(item => (
                <AdvancedJobBox key={item.id} job={item} />
              ))}
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}
