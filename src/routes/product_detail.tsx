import { Background, Controls, ReactFlow } from '@xyflow/react'
import { useMemo } from 'react'
import { useLoaderData } from 'react-router'
import JobPromptNode from '@/components/node/job-prompt'
import JobSearchBg from '@/components/node/job-search-bg'
import type productDetailLoader from '@/loader/product_detail'
import '@xyflow/react/dist/style.css'

export function HydrateFallback() {
  return <div>Loading...</div>
}

export default function Product() {
  const data = useLoaderData<typeof productDetailLoader>()
  console.info('product data: ', data)
  const initialNodes = [
    {
      id: 'n1',
      position: { x: 0, y: 0 },
      data: { label: '提示词' },
      type: 'jobPrompt',
    },
    {
      id: 'n2',
      position: { x: 200, y: 0 },
      data: { label: '搜索背景' },
      type: 'jobSearchBg',
    },
  ]
  const initialEdges = [
    { id: 'n1-n2', source: 'n1', target: 'n2', animated: true },
  ]
  const nodeTypes = useMemo(
    () => ({
      jobPrompt: JobPromptNode,
      jobSearchBg: JobSearchBg,
    }),
    []
  )

  return (
    <div className="size-full overscroll-none">
      <ReactFlow
        nodes={initialNodes}
        edges={initialEdges}
        nodeTypes={nodeTypes}
        fitView
      >
        <Background />
        <Controls />
        {/* <MiniMap nodeStrokeWidth={2} zoomable pannable /> */}
        {/* <Panel position="bottom-center">
          <JobPromptNode />
        </Panel> */}
      </ReactFlow>
    </div>
  )
}
