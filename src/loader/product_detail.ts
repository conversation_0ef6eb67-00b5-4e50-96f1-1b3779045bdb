import type { LoaderFunctionArgs } from 'react-router'
import { webapi } from '@/lib/utils'
import {
  type DiffusionDataType,
  DiffusionStatus,
  JobType,
  type ResponseType,
  useCreationStore,
} from '@/stores/creation'

export default async function clientLoader({ params }: LoaderFunctionArgs) {
  const { jobid: jobId = '' } = params
  let localeJob = useCreationStore.getState().getLocalJob(jobId)

  if (!localeJob) {
    const { data, message } = await webapi
      .get('creations/v1/job', {
        searchParams: {
          jobId,
        },
      })
      .json<ResponseType<DiffusionDataType>>()

    if (data) {
      const {
        incr_id,
        id,
        text,
        status,
        type,
        seed,
        comment,
        urls,
        created_at,
        updated_at,
        is_copyright,
        basemap,
        article,
      } = data
      localeJob = {
        id,
        incr_id,
        text,
        status,
        type,
        seed,
        comment,
        updated_at,
        urls,
        created_at,
        is_copyright,
        basemap,
        article,
      }
    } else {
      localeJob = {
        id: jobId,
        incr_id: 0,
        text: '',
        status: DiffusionStatus.FAILED,
        type: JobType.DEFAULT,
        created_at: '',
        updated_at: '',
        comment: message || '获取任务信息接口调用失败，请稍后重试',
      }
    }
  }

  return localeJob
}
