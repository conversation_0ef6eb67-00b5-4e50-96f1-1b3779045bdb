import { useCallback } from 'react'
import {
  type UploadDataType,
  useAdvancedEditStore,
} from '@/stores/advanced-edit'
import { useCommonStore } from '@/stores/common'

export function useAdvancedUploadSuccess(callback?: () => void) {
  const { setLoading } = useCommonStore()
  const { setWorktop, setForeground } = useAdvancedEditStore()
  const onSuccess = useCallback(
    (info: UploadDataType) => {
      const { url, id, width, height } = info
      const aspectRatio = width / height

      setWorktop({
        originalWidth: width,
        originalHeight: height,
        originalAspectRatio: aspectRatio,
        width: 0,
        height: 0,
      })
      setForeground({
        id,
        type: 'image',
        aspectRatio,
        width,
        height,
        url,
        x: 0,
        y: 0,
        rotate: 0,
      })
      callback?.()
      setLoading(false)
    },
    [setForeground, callback, setLoading, setWorktop]
  )

  return {
    onSuccess,
  }
}
