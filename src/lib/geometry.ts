export type PrimaryAxis = 'x' | 'y'

export type ResizeHandle = 1 | 2 | 3 | 4 // 右下=1, 左下=2, 右上=3, 左上=4

export interface Rect {
  x: number
  y: number
  width: number
  height: number
}

export function scaleToFit(
  containerWidth: number,
  containerHeight: number,
  contentWidth: number,
  contentHeight: number,
  options?: { capScaleAtOne?: boolean }
): number {
  const { capScaleAtOne = true } = options || {}
  const sx = containerWidth / Math.max(1, contentWidth)
  const sy = containerHeight / Math.max(1, contentHeight)
  const scale = Math.min(sx, sy)
  return capScaleAtOne ? Math.min(1, scale) : scale
}

export function fitCenterRect(
  containerWidth: number,
  containerHeight: number,
  contentWidth: number,
  contentHeight: number,
  options?: { capScaleAtOne?: boolean; minSize?: number }
): Rect {
  const { capScaleAtOne = true, minSize = 20 } = options || {}
  const scale = scaleToFit(
    containerWidth,
    containerHeight,
    contentWidth,
    contentHeight,
    { capScaleAtOne }
  )
  const width = Math.max(minSize, Math.round(contentWidth * scale))
  const height = Math.max(minSize, Math.round(contentHeight * scale))
  const x = Math.round((containerWidth - width) / 2)
  const y = Math.round((containerHeight - height) / 2)
  return { x, y, width, height }
}

export function computePrimaryAxis(
  deltaX: number,
  deltaY: number
): PrimaryAxis {
  return Math.abs(deltaX) >= Math.abs(deltaY) ? 'x' : 'y'
}

export function applyCornerResize(
  handle: ResizeHandle,
  image: Rect,
  deltaX: number,
  deltaY: number,
  aspectRatio: number,
  containerWidth: number,
  containerHeight: number,
  primaryAxis: PrimaryAxis,
  options?: { minSize?: number }
): Rect {
  const MIN_SIZE = options?.minSize ?? 20

  let newWidth = image.width
  let newHeight = image.height
  let newX = image.x
  let newY = image.y

  // 先按主轴驱动等比尺寸
  switch (handle) {
    case 1: {
      // 右下 - 锚左上
      if (primaryAxis === 'x') {
        newWidth = image.width + deltaX
        newHeight = newWidth / aspectRatio
      } else {
        newHeight = image.height + deltaY
        newWidth = newHeight * aspectRatio
      }
      break
    }
    case 2: {
      // 左下 - 锚右上
      if (primaryAxis === 'x') {
        newWidth = image.width - deltaX
        newHeight = newWidth / aspectRatio
      } else {
        newHeight = image.height + deltaY
        newWidth = newHeight * aspectRatio
      }
      newX = image.x + (image.width - newWidth)
      break
    }
    case 3: {
      // 右上 - 锚左下
      if (primaryAxis === 'x') {
        newWidth = image.width + deltaX
        newHeight = newWidth / aspectRatio
      } else {
        newHeight = image.height - deltaY
        newWidth = newHeight * aspectRatio
      }
      newY = image.y + (image.height - newHeight)
      break
    }
    case 4: {
      // 左上 - 锚右下
      if (primaryAxis === 'x') {
        newWidth = image.width - deltaX
        newHeight = newWidth / aspectRatio
      } else {
        newHeight = image.height - deltaY
        newWidth = newHeight * aspectRatio
      }
      newX = image.x + (image.width - newWidth)
      newY = image.y + (image.height - newHeight)
      break
    }
  }

  // 尺寸下限保护
  if (newWidth < MIN_SIZE || newHeight < MIN_SIZE) {
    const factor = Math.max(MIN_SIZE / newWidth, MIN_SIZE / newHeight)
    newWidth = Math.max(MIN_SIZE, Math.round(newWidth * factor))
    newHeight = Math.max(MIN_SIZE, Math.round(newHeight * factor))
    // 保持锚点补偿
    switch (handle) {
      case 2:
        newX = image.x + (image.width - newWidth)
        break
      case 3:
        newY = image.y + (image.height - newHeight)
        break
      case 4:
        newX = image.x + (image.width - newWidth)
        newY = image.y + (image.height - newHeight)
        break
    }
  }

  // 边界：不得超过容器（worktop），保持等比，按同一锚点补偿
  if (newWidth > 0 && newHeight > 0) {
    const fit = Math.min(
      1,
      containerWidth / newWidth,
      containerHeight / newHeight
    )
    if (fit < 1) {
      const scaledWidth = newWidth * fit
      const scaledHeight = newHeight * fit
      switch (handle) {
        case 1: {
          newWidth = scaledWidth
          newHeight = scaledHeight
          break
        }
        case 2: {
          newWidth = scaledWidth
          newHeight = scaledHeight
          newX = image.x + (image.width - newWidth)
          break
        }
        case 3: {
          newWidth = scaledWidth
          newHeight = scaledHeight
          newY = image.y + (image.height - newHeight)
          break
        }
        case 4: {
          newWidth = scaledWidth
          newHeight = scaledHeight
          newX = image.x + (image.width - newWidth)
          newY = image.y + (image.height - newHeight)
          break
        }
      }
    }
  }

  return {
    x: Math.round(newX),
    y: Math.round(newY),
    width: newWidth,
    height: newHeight,
  }
}
