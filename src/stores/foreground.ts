import { create } from 'zustand'

type State = {
  position: { x: number; y: number }
  originalSize: { width: number; height: number }
  scale: number
  base64: string
}

type Actions = {
  setPosition: (position: { x: number; y: number }) => void
  setScale: (scale: number) => void
  setOriginalSize: (originalSize: { width: number; height: number }) => void
  setBase64: (base64: string) => void
}

export const useForegroundStore = create<State & Actions>(set => ({
  position: { x: 0, y: 0 },
  originalSize: { width: 0, height: 0 },
  scale: 1,
  base64: '',
  setPosition: position => set({ position }),
  setScale: scale => set({ scale }),
  setOriginalSize: originalSize => set({ originalSize }),
  setBase64: base64 => set({ base64 }),
}))
