import { create } from 'zustand'

interface Tier {
  id: string | number
  x: number
  y: number
  rotate: number
  value?: string
  name?: string
}

export interface TextTier extends Tier {
  type: 'text'
  width: number | 'auto'
  height: number | 'auto'
  direction: 'ltr' | 'rtl'
  writingMode:
    | 'horizontal-tb'
    | 'vertical-rl'
    | 'vertical-lr'
    | 'sideways-rl'
    | 'sideways-lr'
  fontSize: number
  fontFamily: string
  fontWeight: 'normal' | 'bold'
  fontColor: string
}

export interface ImageTier extends Tier {
  type: 'image'
  width: number
  height: number
  aspectRatio: number
  base64: string
  url?: string
}

export type TierType = TextTier | ImageTier
type State = {
  tiers: TierType[]
  currentTier: TierType | null
}

type Actions = {
  setTiers: (tiers: TierType[]) => void
  setNewTier: (tier: TierType) => void
  removeTier: (tier: TierType) => void
  updateTier: (tier: TierType) => void
  setCurrentTier: (tier: TierType | null) => void
  // 图层上移，把当前图层的对象在数组中的位置向后移一位
  moveTierLayerUp: () => void
  // 图层下移，把当前图层的对象在数组中的位置向前移一位
  moveTierLayerDown: () => void
}

export const useTiersStore = create<State & Actions>((set, get) => ({
  tiers: [],
  currentTier: null,
  setTiers: tiers => set({ tiers }),
  setNewTier: tier => set({ tiers: [...get().tiers, tier] }),
  removeTier: tier => set({ tiers: get().tiers.filter(t => t.id !== tier.id) }),
  setCurrentTier: tier => set({ currentTier: tier }),
  updateTier: tier =>
    set({ tiers: get().tiers.map(t => (t.id === tier.id ? tier : t)) }),
  moveTierLayerDown: () => {
    const tier = get().currentTier
    if (!tier) return
    const index = get().tiers.findIndex(t => t.id === tier.id)
    if (index > 0) {
      const newTiers = [...get().tiers]
      ;[newTiers[index], newTiers[index - 1]] = [
        newTiers[index - 1],
        newTiers[index],
      ]
      set({ tiers: newTiers })
    }
  },
  moveTierLayerUp: () => {
    const tier = get().currentTier
    if (!tier) return
    const index = get().tiers.findIndex(t => t.id === tier.id)
    if (index < get().tiers.length - 1) {
      const newTiers = [...get().tiers]
      ;[newTiers[index], newTiers[index + 1]] = [
        newTiers[index + 1],
        newTiers[index],
      ]
      set({ tiers: newTiers })
    }
  },
}))
