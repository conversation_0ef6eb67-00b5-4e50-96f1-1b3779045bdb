import { create } from 'zustand'

type State = {
  position: { x: number; y: number }
  scale: number
}

type Actions = {
  setPosition: (position: { x: number; y: number }) => void
  setScale: (scale: number) => void
}

export const useCutBoxStore = create<State & Actions>(set => ({
  position: { x: 0, y: 0 },
  scale: 1,
  setPosition: position => set({ position }),
  setScale: scale => set({ scale }),
}))
