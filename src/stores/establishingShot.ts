import { create } from 'zustand'
import { createJSONStorage, persist } from 'zustand/middleware'

export type EstablishingShotClassType = 'sref' | 'cref' | 'content' // 风格、人脸、内容

export type EstablishingShotType = {
  id: string
  name?: string
  url: string
}

type State = {
  establishingShots: EstablishingShotType[]
}

type Actions = {
  addEstablishingShots: (establishingShot: EstablishingShotType) => void
  removeEstablishingShots: (id: string) => void
  updateEstablishingShots: (establishingShot: EstablishingShotType) => void
}

export const useEstablishingShotStore = create<State & Actions>()(
  persist(
    (set, get) => ({
      establishingShots: [],
      addEstablishingShots: establishingShot =>
        set({
          establishingShots: [establishingShot, ...get().establishingShots],
        }),
      removeEstablishingShots: id =>
        set({
          establishingShots: get().establishingShots.filter(
            item => item.id !== id
          ),
        }),
      updateEstablishingShots: establishingShot =>
        set({
          establishingShots: get().establishingShots.map(item =>
            item.id === establishingShot.id
              ? { ...item, ...establishingShot }
              : item
          ),
        }),
    }),
    {
      name: 'establishing-shot',
      storage: createJSONStorage(() => localStorage),
    }
  )
)
