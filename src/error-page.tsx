import { useRouteError } from 'react-router'

export default function ErrorPage() {
  const error = useRouteError()

  return (
    <div
      id="error-page"
      className="flex flex-col items-center justify-center h-screen"
    >
      <h1>Oops!</h1>
      <p>Sorry, an unexpected error has occurred.</p>
      <p>
        <i>
          {(error as { statusText?: string; message?: string })?.statusText ||
            (error as { statusText?: string; message?: string })?.message}
        </i>
      </p>
    </div>
  )
}
