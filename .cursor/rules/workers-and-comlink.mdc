---
globs: src/**/*.{ts,tsx}
description: Web Workers 与 Comlink 使用约定
---

# Workers 与 Comlink

- Vite 中启用 `vite-plugin-comlink`，并在 `worker.plugins` 中注入（见 [vite.config.ts](mdc:vite.config.ts)）。
- Workers 位于 [src/workers/](mdc:src/workers)。当前示例：[src/workers/canva.ts](mdc:src/workers/canva.ts)。
- 与主线程交互建议使用 Comlink，暴露明确的函数与类型（避免传输巨大可变对象）。

```ts
// 主线程示例
import { wrap } from 'comlink'

const worker = new Worker(new URL('@/workers/canva.ts', import.meta.url), { type: 'module' })
const api = wrap<{ rasterize: (svg: string) => Promise<ImageData> }>(worker)

async function run(svg: string) {
  const image = await api.rasterize(svg)
  return image
}
```

- Worker 内避免依赖 DOM API；如需处理 Canvas，使用 OffscreenCanvas 或纯算法实现。
