---
globs: src/**/*.{ts,tsx}
description: React Router v7 路由与数据加载（loader）约定
---

# 路由与数据加载

- 路由集中在 [src/main.tsx](mdc:src/main.tsx) 中创建 `createBrowserRouter` 并传入 `children`。
- 页面组件放在 [src/routes/](mdc:src/routes)，命名与路径保持一致。
- 数据加载使用 `loader`，逻辑放置于 [src/loader/](mdc:src/loader)，在路由条目中通过 `loader` 属性挂载。

```tsx
// main.tsx（片段）
{
  path: '/product/:jobid',
  element: <ProductDetail />,
  loader: productDetailLoader,
}
```

- 出错时使用 `errorElement`（见 [src/main.tsx](mdc:src/main.tsx) 的 `ErrorPage`）。
- 路由级代码拆分可通过动态导入实现，但需保持类型与错误边界。
