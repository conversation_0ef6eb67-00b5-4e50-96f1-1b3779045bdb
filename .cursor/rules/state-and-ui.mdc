---
globs: src/**/*.{ts,tsx}
description: Zustand 状态、shadcn/ui 与 Radix UI 的协同约定
---

# 状态管理与 UI 约定

## Zustand

- 全局/复杂业务状态使用 Zustand，放在 [src/stores/](mdc:src/stores)。
- Store 模块使用命名导出与强类型 state/action；避免任意 `any`。
- 将派发的 action 抽象为语义化函数，如 `setActiveLayer(id: string)`，避免在组件内直接操作深层结构。
- 高级编辑相关逻辑集中在 [src/stores/advanced-edit.ts](mdc:src/stores/advanced-edit.ts)。新增能力优先扩展此处的状态与选择器。

```ts
import { create } from 'zustand'

interface CounterState {
  value: number
  increment: (delta?: number) => void
}

export const useCounterStore = create<CounterState>((set) => ({
  value: 0,
  increment: (delta = 1) => set((s) => ({ value: s.value + delta })),
}))
```

## shadcn/ui 与 Radix UI

- 组件位于 [src/components/ui/](mdc:src/components/ui)。优先复用，而非在业务组件内重新实现基础交互。
- 结合 Tailwind 原子类实现样式变体，必要时使用 `class-variance-authority`。
- Tooltip、Dialog、Dropdown 等交互组件使用 Radix 对应实现，保持可访问性。

## 主题与暗色

- 通过 [src/components/theme-provider.tsx](mdc:src/components/theme-provider.tsx) 管理主题（`class` 暗色模式）。
- 在组件中以 `className` 方式使用 `dark:` 前缀实现暗色适配。
