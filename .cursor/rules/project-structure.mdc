---
alwaysApply: true
description: 项目结构与关键入口，帮助 AI 快速定位入口、配置与核心目录
---

# 项目结构与关键入口

- 入口与挂载
  - HTML 入口：[index.html](mdc:index.html)
  - 应用入口与路由装配：[src/main.tsx](mdc:src/main.tsx)

- 构建与别名
  - Vite 配置（`@` 指向 `./src`、SWC、Comlink）：[vite.config.ts](mdc:vite.config.ts)
  - TypeScript 路径与基准目录：
    - [tsconfig.json](mdc:tsconfig.json)
    - [tsconfig.app.json](mdc:tsconfig.app.json)

- 路由与页面
  - 路由目录（React Router v7）：[src/routes/](mdc:src/routes)
  - 数据加载器（配合路由 `loader` 使用）：[src/loader/](mdc:src/loader)

- 状态管理
  - 全局与业务状态（Zustand）：[src/stores/](mdc:src/stores)
  - 高级编辑核心状态： [src/stores/advanced-edit.ts](mdc:src/stores/advanced-edit.ts)

- UI 与样式
  - 全局样式与 Tailwind 引入：[src/index.css](mdc:src/index.css)
  - Tailwind 配置（暗色模式、tokens、插件）：[tailwind.config.ts](mdc:tailwind.config.ts)
  - 主题容器与 Provider：[src/components/theme-provider.tsx](mdc:src/components/theme-provider.tsx)
  - 设计系统组件（shadcn/ui）：[src/components/ui/](mdc:src/components/ui)

- 配置与常量
  - 平台/端点配置：[src/config/index.ts](mdc:src/config/index.ts)

- Workers
  - Web Worker 与 Comlink 示例：[src/workers/canva.ts](mdc:src/workers/canva.ts)

- 资产与字体
  - 静态资源与中文字体：[public/](mdc:public) | [public/fonts/](mdc:public/fonts)

- 质量与脚本
  - Biome（格式化与 Lint）：[biome.json](mdc:biome.json)
  - 包管理与脚本：[package.json](mdc:package.json)

## 关键约定

- 使用 `@` 作为绝对导入前缀，指向 `./src`（详见 Vite 与 TS 配置）。
- 新组件放置于 `[src/components/](mdc:src/components)`，页面级组件位于 `[src/routes/](mdc:src/routes)`。
- 新的全局/业务状态放入 `[src/stores/](mdc:src/stores)` 并保持强类型定义。
- 与路由相关的数据请求与预处理放入 `[src/loader/](mdc:src/loader)` 并通过路由 `loader` 关联。
