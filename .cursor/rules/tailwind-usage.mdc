---
globs: src/**/*.tsx
description: Tailwind CSS 使用约定、设计令牌与 shadcn/ui 协同
---

# Tailwind CSS 使用约定

- 配置：暗色模式 `class`、设计令牌、动画等见 [tailwind.config.ts](mdc:tailwind.config.ts)。
- 扫描范围：`./index.html` 与 `./src/**/*.{js,ts,jsx,tsx}`。
- 主题：通过 CSS 变量与 `ThemeProvider`（见 [src/components/theme-provider.tsx](mdc:src/components/theme-provider.tsx)）。
- 组件：优先使用 shadcn/ui 组件于 [src/components/ui/](mdc:src/components/ui)。

## 书写风格

- 原子化优先：在 JSX 中直接组合类名，减少自定义 CSS。
- 控制可读性：按布局 → 间距 → 排版 → 颜色 → 状态顺序组织类名。
- 避免过度 `@apply`；复杂条件使用 `clsx`/`class-variance-authority`（已在依赖）。

```tsx
import { Button } from '@/components/ui/button'

export const CTA = () => (
  <div className="flex flex-col items-center gap-4 p-6 text-center">
    <h2 className="text-2xl font-semibold tracking-tight">标题</h2>
    <p className="text-muted-foreground">说明文本</p>
    <Button className="px-6">开始使用</Button>
  </div>
)
```

## 响应式与暗色模式

- 使用 `sm:`/`md:`/`lg:` 前缀进行响应式布局。
- 通过 `dark:` 前缀适配暗色主题。

## 背景网格与动画

- 已提供背景图样式键：`bg-white-box`、`bg-black-box`；尺寸：`bg-16px`、`bg-8px`。
- 动画：`accordion-down`、`accordion-up` 可复用。
