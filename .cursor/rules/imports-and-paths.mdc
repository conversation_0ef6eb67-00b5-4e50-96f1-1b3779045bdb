---
globs: *.ts,*.tsx
description: 导入顺序、路径别名与类型导入约定
---

# 导入与路径约定

- 绝对导入：使用 `@/` 指向 `src/`（见 [vite.config.ts](mdc:vite.config.ts)、[tsconfig.json](mdc:tsconfig.json)）。
- 导入顺序：
  1. React/类型
  2. 第三方库
  3. 应用内绝对导入（`@/…`）
  4. 相对导入
- 类型仅导入使用 `import type { X } from '…'`，减少运行时代码。
- 避免深层级相对路径（如 `../../../`），统一改为 `@/…`。

```ts
import { useMemo } from 'react'
import { z } from 'zod'
import { Button } from '@/components/ui/button'
import { formatDate } from '@/utils/dateUtils'
```

- 新增路径别名需同步更新 Vite 与 TS `paths`；并保持 CI/IDE 解析一致。
