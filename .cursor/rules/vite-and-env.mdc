---
alwaysApply: true
description: Vite 配置、别名、环境变量与构建脚本的约定
---

# Vite 与环境变量

- **插件**：使用 `@vitejs/plugin-react-swc` 与 `vite-plugin-comlink`（见 [vite.config.ts](mdc:vite.config.ts)）。
- **别名**：`@` → `./src`，用于绝对导入（Vite 与 TS 路径一致，见 [tsconfig.json](mdc:tsconfig.json)）。
- **Workers**：通过 `worker.plugins` 注入 Comlink 插件，Worker 放于 `[src/workers/](mdc:src/workers)`。
- **样式**：Tailwind 在 [src/index.css](mdc:src/index.css) 引入，配置见 [tailwind.config.ts](mdc:tailwind.config.ts)。

## 环境变量

- 使用 `import.meta.env.VITE_*` 访问变量。
- 类型声明位于 [src/vite-env.d.ts](mdc:src/vite-env.d.ts)。
- 建议：将敏感值放在 `.env.local`，不提交版本库；公共只读配置可放 `.env`。

## 常用脚本（pnpm）

- 开发：`pnpm dev`
- 构建：`pnpm build`
- 预览：`pnpm preview`
- 质量：`pnpm lint`、`pnpm format`、`pnpm check`

## 约定

- 新增入口或别名时，同时更新 Vite 与 TS `paths`。
- 页面的路由定义集中在 [src/main.tsx](mdc:src/main.tsx)。
