# Repository Guidelines

## Project Structure & Module Organization
The app boots from `src/main.tsx`, pulling shared styling from `src/index.css`. Keep feature routes under `src/routes/`, lift reusable UI into `src/components/`, and colocate state with Zustand stores in `src/stores/`. Reusable hooks live in `src/hooks/`, while configuration, helpers, and third-party adapters belong in `src/config/` and `src/lib/`. Web Workers must be wired through Comlink under `src/workers/`. Ship processed assets from `src/assets/` and drop static files in `public/`. Update README diagrams if you introduce new top-level folders.

## Build, Test, and Development Commands
Run `pnpm install` (≥ v10.15.0) before development. Use `pnpm dev` for the Vite dev server, `pnpm build` for production bundles, and `pnpm preview` to smoke-test builds locally. Quality gates: `pnpm lint` (Biome static analysis), `pnpm lint:fix` (auto-fix), `pnpm format` (Biome formatting), and `pnpm check` for the combined lint + type suite. Reach for `pnpm build:analyze` when you need bundle insight.

## Coding Style & Naming Conventions
Biome enforces 2-space indentation, 80-character lines, and single quotes in TS/JS (double quotes in JSX). Order imports React → third-party → `@/` aliases → relative paths, marking type-only imports with `type`. Prefer camelCase for variables/functions, PascalCase for components, types, and stores, and kebab-case filenames. Keep Tailwind classes atomic and mirror light/dark variants (`bg-muted dark:bg-muted/80`). Exported module APIs require explicit TypeScript types.

## Testing Guidelines
Automated tests are not yet wired; rely on `pnpm check` for lint and type coverage. Document manual verification for key flows—route navigation, advanced search, and image dialog interactions—in your PR description or attach short clips. Name any new test utilities after their domain (`creation.test-helper.ts`) and park them beside the feature they serve.

## Commit & Pull Request Guidelines
Write Conventional Commits (`type(scope): summary`), e.g., `feat(routes): add gallery filters` or `chore(依赖): 升级 pnpm`. Rebase before pushing, note executed commands (at minimum `pnpm check`), and link issues or tickets. For UI changes, include screenshots or captures. Flag modifications touching stores, workers, or config files so reviewers can double-check side effects.

## Security & Configuration Tips
Expose browser-readable secrets only with the `VITE_` prefix. Limit config edits to `vite.config.ts`, `tailwind.config.ts`, or files under `src/config/`, and update `README.md` when toggles change. Persist user preferences through clearly named Zustand actions to keep stored keys discoverable.
