// PromptCommandParser 使用示例

import { PromptCommandParser } from '@/lib/utils'

// 示例 1: 解析复杂命令
const complexCommand = `https://example.com/image1.jpg https://example.com/image2.jpg 一个美丽的风景画 --ar 16:9 --v 6.1 --chaos 25 --quality 0.8 --sref https://example.com/style1.jpg --cref https://example.com/face1.jpg --raw --fast`

const result = PromptCommandParser.parse(complexCommand)
if (result.success) {
  console.log('解析成功:')
  console.log(PromptCommandParser.formatResult(result.data))
  
  // 验证命令
  const validationErrors = PromptCommandParser.validate(result.data)
  if (validationErrors.length === 0) {
    console.log('命令验证通过')
  } else {
    console.log('验证错误:', validationErrors)
  }
} else {
  console.log('解析失败:', result.errors)
}

// 示例 2: 使用构建器创建命令
const command = PromptCommandParser.builder()
  .setContentUrls(['https://example.com/content.jpg'])
  .setPrompt('一个可爱的小猫')
  .setAspectRatio(1, 1)
  .setModelVersion('6.1')
  .setChaos(30)
  .setQuality(0.9)
  .addStyleUrl('https://example.com/style.jpg')
  .setVideoMode(true)
  .setMotion('high')
  .setBatchSize(2)
  .build()

console.log('构建的命令:', command.toString())
console.log('格式化结果:')
console.log(PromptCommandParser.formatResult(command))

// 示例 3: 视频生成命令
const videoCommand = `https://example.com/first-frame.jpg 将这张图片转换为动态视频 --ar 16:9 --motion high --bs 2 --end https://example.com/end-frame.jpg --loop --fast`

const videoResult = PromptCommandParser.parse(videoCommand)
if (videoResult.success) {
  console.log('视频命令解析成功:')
  console.log(PromptCommandParser.formatResult(videoResult.data))
}