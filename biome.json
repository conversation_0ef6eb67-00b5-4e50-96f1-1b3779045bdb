{"$schema": "https://biomejs.dev/schemas/v2.1.3/schema.json", "vcs": {"enabled": true, "clientKind": "git", "useIgnoreFile": true}, "files": {"ignoreUnknown": false, "includes": ["src/**/*"]}, "formatter": {"enabled": true, "formatWithErrors": false, "indentStyle": "space", "indentWidth": 2, "lineEnding": "lf", "lineWidth": 80, "attributePosition": "auto"}, "linter": {"enabled": true, "rules": {"recommended": true, "a11y": {"recommended": true}, "complexity": {"recommended": true}, "correctness": {"recommended": true}, "performance": {"recommended": true}, "security": {"recommended": true}, "style": {"recommended": true}, "suspicious": {"recommended": true}}}, "javascript": {"formatter": {"quoteStyle": "single", "jsxQuoteStyle": "double", "quoteProperties": "asNeeded", "trailingCommas": "es5", "semicolons": "asNeeded", "arrowParentheses": "asNeeded", "bracketSpacing": true, "bracketSameLine": false}}, "json": {"formatter": {"trailingCommas": "none"}}}