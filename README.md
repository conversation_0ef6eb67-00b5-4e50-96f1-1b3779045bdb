# Demo AI - AI 智能内容创作平台

Demo AI 是一个基于 React 的 AI 智能内容创作平台，专为中文用户和社交媒体优化设计。该平台提供强大的 AI 图像、视频和文章生成功能，并集成了先进的图像编辑工具。

## 🌟 核心功能

### AI 内容生成
- **多模态 AI 生成**：支持图像、视频和文章的智能创作
- **图像处理**：变体生成、高清放大、重混、平移、背景移除
- **视频功能**：视频生成、扩展、高清放大
- **文章创作**：智能文章生成和编辑

### 高级图像编辑
- **图层管理**：基于画布的图层式图像处理
- **参考系统**：风格、内容和面部参考功能
- **实时编辑**：支持历史记录的高级编辑界面
- **批量处理**：支持批量导出和处理

### 社交媒体优化
- **中文平台适配**：针对微信、微博、小红书、抖音等平台优化
- **预设尺寸**：内置各大社交平台的标准图片尺寸
- **专业导出**：支持高质量的专业级导出功能

## 🛠 技术栈

### 前端技术
- **React 18** + **TypeScript** - 现代化前端开发框架
- **Vite** + **SWC** - 高性能构建工具
- **React Router v7** - 路由管理
- **Zustand** - 轻量级状态管理

### UI 组件
- **Tailwind CSS** - 原子化 CSS 框架
- **shadcn/ui** - 现代化 UI 组件库（基于 Radix UI）
- **Lucide React** - 图标库
- **next-themes** - 主题切换

### 核心依赖
- **ky** - HTTP 客户端，支持拦截器
- **react-hook-form** + **zod** - 表单处理和验证
- **dayjs** - 日期处理（中文本地化）
- **comlink** - Web Worker 通信
- **ahooks** - React 实用 hooks

### 开发工具
- **Biome** - 代码格式化和 Linting（替代 ESLint/Prettier）
- **pnpm** - 包管理器

## 📁 项目结构

```
src/
├── routes/              # 应用页面路由
│   ├── home.tsx        # 主页（创作界面）
│   ├── creation_detail.tsx  # 任务详情和编辑
│   ├── advanced_edit.tsx     # 高级编辑（历史记录）
│   ├── advanced_edit_new.tsx # 复杂图像编辑
│   ├── product.tsx     # 产品页面
│   └── login.tsx       # 用户认证
├── stores/              # Zustand 状态管理
│   ├── common.ts       # 全局状态和认证
│   ├── creation.ts     # AI 生成引擎
│   ├── advanced-edit.ts # 高级编辑功能
│   ├── edit.ts         # 基础图层管理
│   ├── cutbox.ts       # 画布定位
│   └── establishingShot.ts # 参考图片
├── components/          # UI 组件
│   ├── creation/       # 任务生成界面组件
│   ├── advanced-editing/ # 画布编辑工具
│   └── ui/             # shadcn/ui 设计系统组件
├── config/             # 配置文件
│   └── index.ts        # API 端点和平台预设
├── workers/            # Web Workers
└── public/fonts/       # 中文字体资源
```

## 🎯 支持的任务类型

平台支持 11 种不同的 AI 任务类型：

1. **图像变体** - 生成图像的多种变体
2. **图像放大** - 智能高清放大
3. **图像重混** - 风格和内容混合
4. **图像平移** - 画面扩展
5. **背景移除** - 智能抠图
6. **视频生成** - AI 视频创作
7. **视频扩展** - 视频内容延长
8. **视频放大** - 视频高清化
9. **文章生成** - 智能写作
10. **任务状态追踪** - 实时状态更新
11. **批量处理** - 批量任务管理

## 🚀 快速开始

### 环境要求
- Node.js 18+
- pnpm 9+

### 安装依赖
```bash
pnpm install
```

### 开发环境
```bash
pnpm dev
```
启动开发服务器，支持热重载

### 构建生产版本
```bash
pnpm build
```

### 预览生产构建
```bash
pnpm preview
```

## 🔧 开发命令

### 代码质量
```bash
pnpm lint           # 运行 Biome Linter
pnpm lint:fix       # 自动修复 Lint 问题
pnpm format         # 格式化代码
pnpm format:check   # 检查代码格式
pnpm check          # 运行综合检查（lint + format）
pnpm check:fix      # 自动修复所有问题
```

## 🌐 API 配置

- **生产环境 API**：`https://demo-ai.elephantailab.com/api`
- **Midjourney API**：`https://api.fotomore.com/api/creations/v1`

## 📱 社交媒体尺寸预设

平台内置了主流中文社交媒体平台的标准尺寸：

### 微信系列
- 公众号封面：900×383
- 朋友圈：1080×1080
- 视频号横版/竖版封面
- 图文内容：1242×1660

### 微博
- 主页背景图：980×300

### 小红书
- 主页背景图：1000×800
- 图文内容（横版/竖版）

### 抖音
- 主页背景图：1125×633
- 图文和视频封面（横版/竖版）

## 🎨 特色功能

- **智能参考系统**：支持风格、内容和面部参考
- **实时状态追踪**：WebSocket 风格的任务状态更新
- **图层式编辑**：专业级图像编辑体验
- **中文本地化**：完整的中文界面和字体支持
- **响应式设计**：支持多种设备和屏幕尺寸

## 📄 许可证

本项目为私有项目，版权所有。

---

*Demo AI - 让 AI 创作更简单，让内容创作更智能*