import { defineConfig } from 'vite'
import react from '@vitejs/plugin-react-swc'
import path from 'path'
import { comlink } from 'vite-plugin-comlink'

// https://vitejs.dev/config/
export default defineConfig(({ mode }) => ({
  plugins: [comlink(), react()],
  resolve: {
    alias: {
      '@': path.resolve(__dirname, './src')
    }
  },
  worker: {
    plugins: () => [comlink()],
  },
  build: {
    // 增加 chunk 大小警告限制
    chunkSizeWarningLimit: 1000,
    // 只在生产环境启用 CSS 代码分割
    cssCodeSplit: mode === 'production',
    rollupOptions: {
      output: {
        // 手动分块策略
        manualChunks: (id) => {
          // React 核心库
          if (id.includes('react') || id.includes('react-dom') || id.includes('react-router')) {
            return 'react-vendor'
          }
          // UI 组件库
          if (id.includes('@radix-ui/')) {
            return 'ui-vendor'
          }
          // 工具库
          if (id.includes('clsx') || id.includes('class-variance-authority') ||
              id.includes('tailwind-merge') || id.includes('lucide-react') ||
              id.includes('date-fns') || id.includes('dayjs') ||
              id.includes('radash') || id.includes('lodash')) {
            return 'utils-vendor'
          }
          // 状态管理
          if (id.includes('zustand') || id.includes('idb-keyval')) {
            return 'state-vendor'
          }
          // HTTP 客户端
          if (id.includes('ky') || id.includes('comlink')) {
            return 'http-vendor'
          }
          // 表单处理
          if (id.includes('react-hook-form') || id.includes('zod') || id.includes('@hookform/resolvers')) {
            return 'form-vendor'
          }
          // 图表和可视化
          if (id.includes('recharts')) {
            return 'chart-vendor'
          }
          // 文件处理
          if (id.includes('file-saver') || id.includes('dom-to-image')) {
            return 'file-vendor'
          }
          // 其他工具
          if (id.includes('ahooks') || id.includes('react-color') ||
              id.includes('react-day-picker') || id.includes('react-markdown') ||
              id.includes('react-resizable-panels') || id.includes('sonner') ||
              id.includes('vaul')) {
            return 'misc-vendor'
          }
        }
      }
    }
  }
}))
